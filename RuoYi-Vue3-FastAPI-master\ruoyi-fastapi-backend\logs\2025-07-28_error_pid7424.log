2025-07-28 16:20:36.119 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 16:20:36.120 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 16:20:36.147 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 16:20:36.147 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 16:20:36.149 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 16:20:36.182 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 16:20:36.215 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 16:20:36.215 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 16:21:12.952 | d5f8464455004348892e7b7345b02708 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 16:21:12.952 | d5f8464455004348892e7b7345b02708 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 16:21:12.952 | d5f8464455004348892e7b7345b02708 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 16:21:12.952 | d5f8464455004348892e7b7345b02708 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 16:21:12.953 | d5f8464455004348892e7b7345b02708 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 16:21:12.953 | d5f8464455004348892e7b7345b02708 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 16:21:12.953 | d5f8464455004348892e7b7345b02708 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 16:21:12.953 | d5f8464455004348892e7b7345b02708 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 16:21:12.953 | d5f8464455004348892e7b7345b02708 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 16:21:12.960 | d5f8464455004348892e7b7345b02708 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-28 16:21:12.960 | d5f8464455004348892e7b7345b02708 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 16:21:12.960 | d5f8464455004348892e7b7345b02708 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-28 16:21:12.960 | d5f8464455004348892e7b7345b02708 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-28 16:21:12.962 | d5f8464455004348892e7b7345b02708 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 16:21:12.962 | d5f8464455004348892e7b7345b02708 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-28 16:21:12.962 | d5f8464455004348892e7b7345b02708 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 16:21:12.962 | d5f8464455004348892e7b7345b02708 | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-28 16:21:14.756 | b575fd65f40147ad8334e5a186b873c5 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 16:21:14.756 | b575fd65f40147ad8334e5a186b873c5 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 16:21:14.757 | b575fd65f40147ad8334e5a186b873c5 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 16:21:14.757 | b575fd65f40147ad8334e5a186b873c5 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 16:21:14.757 | b575fd65f40147ad8334e5a186b873c5 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 16:21:14.757 | b575fd65f40147ad8334e5a186b873c5 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 16:21:14.758 | b575fd65f40147ad8334e5a186b873c5 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 16:21:14.758 | b575fd65f40147ad8334e5a186b873c5 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 16:21:14.759 | b575fd65f40147ad8334e5a186b873c5 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 16:21:14.760 | b575fd65f40147ad8334e5a186b873c5 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 16:21:14.767 | b575fd65f40147ad8334e5a186b873c5 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-28 16:21:14.767 | b575fd65f40147ad8334e5a186b873c5 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 16:21:14.767 | b575fd65f40147ad8334e5a186b873c5 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-28 16:21:14.767 | b575fd65f40147ad8334e5a186b873c5 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-28 16:21:14.770 | b575fd65f40147ad8334e5a186b873c5 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 16:21:14.770 | b575fd65f40147ad8334e5a186b873c5 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-28 16:21:14.770 | b575fd65f40147ad8334e5a186b873c5 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 16:21:14.770 | b575fd65f40147ad8334e5a186b873c5 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 16:21:23.884 | 7dc21bdfc13242969685adfe9baa1890 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 16:21:29.705 | 9ea56f69b76646cbad277ef896cd5ae8 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 16:21:29.705 | 9ea56f69b76646cbad277ef896cd5ae8 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 16:21:29.705 | 9ea56f69b76646cbad277ef896cd5ae8 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 16:21:29.705 | 9ea56f69b76646cbad277ef896cd5ae8 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 16:21:29.706 | 9ea56f69b76646cbad277ef896cd5ae8 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 16:21:29.706 | 9ea56f69b76646cbad277ef896cd5ae8 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 16:21:29.706 | 9ea56f69b76646cbad277ef896cd5ae8 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 16:21:29.706 | 9ea56f69b76646cbad277ef896cd5ae8 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 16:21:29.706 | 9ea56f69b76646cbad277ef896cd5ae8 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 16:21:29.707 | 9ea56f69b76646cbad277ef896cd5ae8 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 16:21:29.710 | 9ea56f69b76646cbad277ef896cd5ae8 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-28 16:21:29.710 | 9ea56f69b76646cbad277ef896cd5ae8 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 16:21:29.711 | 9ea56f69b76646cbad277ef896cd5ae8 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-28 16:21:29.711 | 9ea56f69b76646cbad277ef896cd5ae8 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-28 16:21:29.712 | 9ea56f69b76646cbad277ef896cd5ae8 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 16:21:29.712 | 9ea56f69b76646cbad277ef896cd5ae8 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-28 16:21:29.712 | 9ea56f69b76646cbad277ef896cd5ae8 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 16:21:29.712 | 9ea56f69b76646cbad277ef896cd5ae8 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 16:27:05.346 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 16:27:05.346 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
