"""
智驱力算法包跟踪器基类
"""

import numpy as np
from typing import List, Dict, Tuple, Optional


class Tracker:
    """目标跟踪器基类"""
    
    def __init__(self, frame_interval: int = 1, max_disappeared: int = 30, max_distance: float = 50):
        """
        初始化跟踪器

        Args:
            frame_interval: 帧间隔
            max_disappeared: 目标消失的最大帧数
            max_distance: 目标匹配的最大距离
        """
        self.next_object_id = 1  # 从1开始，避免ID过大
        self.objects = {}
        self.disappeared = {}
        self.max_disappeared = max_disappeared
        self.max_distance = max_distance

        # 添加车辆计数算法需要的属性
        self.frame_interval = frame_interval
        self.track_buffer = max_disappeared  # track_buffer等于max_disappeared
        self.tracks = {}  # 跟踪轨迹

        # 添加时间和计数相关的属性
        import time
        self.start_time = time.time()
        self.frame_count = 0
    
    def register(self, centroid: Tuple[float, float]) -> int:
        """
        注册新目标，基于时间和计数生成合理的车辆ID

        Args:
            centroid: 目标中心点坐标

        Returns:
            新目标的ID
        """
        # 生成基于时间和计数的合理ID，避免ID过大
        # ID格式：简单递增，从1开始，最大不超过999
        vehicle_id = self.next_object_id
        if self.next_object_id > 999:
            # 如果ID超过999，重置为1（避免ID过大）
            self.next_object_id = 1
            vehicle_id = 1

        self.objects[vehicle_id] = centroid
        self.disappeared[vehicle_id] = 0
        self.next_object_id += 1

        # 记录日志
        from logger import LOGGER
        LOGGER.info(f"注册新车辆ID: {vehicle_id}, 当前活跃车辆数: {len(self.objects)}")

        return vehicle_id
    
    def deregister(self, object_id: int):
        """
        注销目标
        
        Args:
            object_id: 目标ID
        """
        if object_id in self.objects:
            del self.objects[object_id]
        if object_id in self.disappeared:
            del self.disappeared[object_id]
    
    def update(self, detections: List[Dict]) -> Dict[int, Dict]:
        """
        更新跟踪器
        
        Args:
            detections: 检测结果列表，每个元素包含边界框信息
            
        Returns:
            跟踪结果字典，键为目标ID，值为目标信息
        """
        # 如果没有检测结果
        if len(detections) == 0:
            # 标记所有现有目标为消失
            for object_id in list(self.disappeared.keys()):
                self.disappeared[object_id] += 1
                
                # 如果目标消失时间过长，则删除
                if self.disappeared[object_id] > self.max_disappeared:
                    self.deregister(object_id)
            
            return self._format_tracking_result()
        
        # 计算检测结果的中心点
        input_centroids = []
        for detection in detections:
            if 'xyxy' in detection:
                x1, y1, x2, y2 = detection['xyxy']
                cx = int((x1 + x2) / 2.0)
                cy = int((y1 + y2) / 2.0)
                input_centroids.append((cx, cy))
            else:
                # 如果没有边界框信息，跳过
                continue
        
        # 如果没有现有目标，注册所有检测结果
        if len(self.objects) == 0:
            for centroid in input_centroids:
                self.register(centroid)
        else:
            # 计算现有目标和新检测结果之间的距离
            object_centroids = list(self.objects.values())
            object_ids = list(self.objects.keys())
            
            # 计算距离矩阵
            D = self._compute_distance_matrix(object_centroids, input_centroids)
            
            # 执行匈牙利算法进行匹配
            rows, cols = self._hungarian_assignment(D)
            
            # 处理匹配结果
            used_row_indices = set()
            used_col_indices = set()
            
            for (row, col) in zip(rows, cols):
                if D[row, col] > self.max_distance:
                    continue
                
                object_id = object_ids[row]
                self.objects[object_id] = input_centroids[col]
                self.disappeared[object_id] = 0
                
                used_row_indices.add(row)
                used_col_indices.add(col)
            
            # 处理未匹配的目标和检测结果
            unused_row_indices = set(range(0, D.shape[0])).difference(used_row_indices)
            unused_col_indices = set(range(0, D.shape[1])).difference(used_col_indices)
            
            # 如果未匹配的目标数量大于等于未匹配的检测结果数量
            if D.shape[0] >= D.shape[1]:
                for row in unused_row_indices:
                    object_id = object_ids[row]
                    self.disappeared[object_id] += 1
                    
                    if self.disappeared[object_id] > self.max_disappeared:
                        self.deregister(object_id)
            else:
                # 注册新的检测结果
                for col in unused_col_indices:
                    self.register(input_centroids[col])
        
        return self._format_tracking_result()
    
    def _compute_distance_matrix(self, object_centroids: List[Tuple], input_centroids: List[Tuple]) -> np.ndarray:
        """计算距离矩阵"""
        D = np.linalg.norm(np.array(object_centroids)[:, np.newaxis] - np.array(input_centroids), axis=2)
        return D
    
    def _hungarian_assignment(self, distance_matrix: np.ndarray) -> Tuple[List, List]:
        """简化的匈牙利算法实现"""
        # 这里使用简单的贪心算法代替完整的匈牙利算法
        rows, cols = [], []
        used_cols = set()
        
        for i in range(distance_matrix.shape[0]):
            min_col = -1
            min_dist = float('inf')
            
            for j in range(distance_matrix.shape[1]):
                if j not in used_cols and distance_matrix[i, j] < min_dist:
                    min_dist = distance_matrix[i, j]
                    min_col = j
            
            if min_col != -1:
                rows.append(i)
                cols.append(min_col)
                used_cols.add(min_col)
        
        return rows, cols
    
    def _format_tracking_result(self) -> Dict[int, Dict]:
        """格式化跟踪结果"""
        result = {}
        for object_id, centroid in self.objects.items():
            result[object_id] = {
                'id': object_id,
                'centroid': centroid,
                'disappeared': self.disappeared.get(object_id, 0)
            }
        return result

    def track(self, rectangles: List[Dict]) -> Dict[int, Dict]:
        """
        跟踪目标，生成合理的车辆ID

        Args:
            rectangles: 检测框列表，每个元素包含xyxy坐标

        Returns:
            跟踪结果字典，key为track_id，value为目标信息
        """
        # 更新帧计数
        self.frame_count += 1

        # 如果没有检测框，增加所有目标的消失计数
        if not rectangles:
            for object_id in list(self.disappeared.keys()):
                self.disappeared[object_id] += 1
                if self.disappeared[object_id] > self.max_disappeared:
                    self.deregister(object_id)
            return self._format_tracking_result()

        # 提取检测框的中心点
        input_centroids = []
        for rect in rectangles:
            if 'xyxy' in rect:
                x1, y1, x2, y2 = rect['xyxy']
                cx = (x1 + x2) / 2.0
                cy = (y1 + y2) / 2.0
                input_centroids.append((cx, cy))

        # 如果没有现有目标，注册所有新目标
        if len(self.objects) == 0:
            for i, centroid in enumerate(input_centroids):
                track_id = self.register(centroid)
                self.tracks[track_id] = rectangles[i].copy()
                self.tracks[track_id]['track_id'] = track_id
        else:
            # 使用匈牙利算法进行目标匹配
            object_centroids = list(self.objects.values())
            D = self._compute_distance_matrix(object_centroids, input_centroids)

            if D.size > 0:
                rows, cols = self._hungarian_assignment(D)

                # 处理匹配的目标
                used_row_indices = set()
                used_col_indices = set()

                for row, col in zip(rows, cols):
                    if D[row, col] <= self.max_distance:
                        object_id = list(self.objects.keys())[row]
                        self.objects[object_id] = input_centroids[col]
                        self.disappeared[object_id] = 0

                        # 更新跟踪信息
                        self.tracks[object_id] = rectangles[col].copy()
                        self.tracks[object_id]['track_id'] = object_id

                        used_row_indices.add(row)
                        used_col_indices.add(col)

                # 处理未匹配的检测框（注册新目标）
                unused_col_indices = set(range(len(input_centroids))) - used_col_indices
                for col in unused_col_indices:
                    track_id = self.register(input_centroids[col])
                    self.tracks[track_id] = rectangles[col].copy()
                    self.tracks[track_id]['track_id'] = track_id

                # 处理未匹配的现有目标（增加消失计数）
                unused_row_indices = set(range(len(object_centroids))) - used_row_indices
                for row in unused_row_indices:
                    object_id = list(self.objects.keys())[row]
                    self.disappeared[object_id] += 1
                    if self.disappeared[object_id] > self.max_disappeared:
                        if object_id in self.tracks:
                            del self.tracks[object_id]
                        self.deregister(object_id)

        # 返回当前跟踪结果
        result = {}
        for track_id in self.tracks:
            if track_id in self.objects:  # 确保目标仍然存在
                # 深度复制避免引用问题
                import copy
                result[track_id] = copy.deepcopy(self.tracks[track_id])

        return result
