2025-07-28 15:30:44.648 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 15:30:44.649 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 15:30:44.676 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 15:30:44.676 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 15:30:44.678 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 15:30:44.712 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 15:30:44.723 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 15:30:44.724 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 15:30:47.552 | 8447785bb6d244beba1d2e994bba2435 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 15:30:47.571 | 65133796e7fb481c8e4abac9974b80b0 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 15:30:47.586 | 0637719667ce4f2d8a06bea3b7f12e1f | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 15:30:47.630 | dc88961d78ad44848ce72dc3ef516a29 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 15:30:47.860 | 66804fd55f804f21b95ed4b1cedf9236 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 15:30:51.586 | 0fa83190f5de492da6bacf38ae66da49 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 15:30:51.587 | 0fa83190f5de492da6bacf38ae66da49 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 15:30:51.587 | 0fa83190f5de492da6bacf38ae66da49 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 15:30:51.587 | 0fa83190f5de492da6bacf38ae66da49 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 15:30:51.588 | 0fa83190f5de492da6bacf38ae66da49 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 15:30:51.588 | 0fa83190f5de492da6bacf38ae66da49 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 15:30:51.588 | 0fa83190f5de492da6bacf38ae66da49 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 15:30:51.588 | 0fa83190f5de492da6bacf38ae66da49 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 15:30:51.589 | 0fa83190f5de492da6bacf38ae66da49 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 15:30:51.594 | 0fa83190f5de492da6bacf38ae66da49 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-28 15:30:51.594 | 0fa83190f5de492da6bacf38ae66da49 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 15:30:51.594 | 0fa83190f5de492da6bacf38ae66da49 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-28 15:30:51.594 | 0fa83190f5de492da6bacf38ae66da49 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-28 15:30:51.596 | 0fa83190f5de492da6bacf38ae66da49 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 15:30:51.596 | 0fa83190f5de492da6bacf38ae66da49 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-28 15:30:51.596 | 0fa83190f5de492da6bacf38ae66da49 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 15:30:51.596 | 0fa83190f5de492da6bacf38ae66da49 | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-28 15:30:53.022 | 61bd9aaf90114fd7a340724e11f4ab54 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 15:31:01.154 | fa7d3672597e4d89911b1ad8ab91035c | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 15:33:10.813 | 6fc09330aeda4f07b258577aa7523995 | INFO     | module_stream.controller.stream_controller:get_stream_manage_stream_list:282 - 接收到的搜索参数: pageNum=1, pageSize=10, streamName=None, rtspUrl=None, location=None, status=None, isRecording=None
2025-07-28 15:33:10.814 | 6fc09330aeda4f07b258577aa7523995 | INFO     | module_stream.controller.stream_controller:get_stream_manage_stream_list:297 - 构建的查询对象: {'stream_id': None, 'user_id': 1, 'stream_name': None, 'rtsp_url': None, 'location': None, 'stream_config': None, 'status': None, 'is_recording': None, 'del_flag': None, 'create_by': None, 'create_time': None, 'update_by': None, 'update_time': None, 'remark': None, 'page_num': 1, 'page_size': 10}
2025-07-28 15:33:10.817 | 6fc09330aeda4f07b258577aa7523995 | INFO     | module_stream.controller.stream_controller:get_stream_manage_stream_list:300 - 获取成功
2025-07-28 15:33:13.546 | afde54a38e5d4b9bb1f503990c302d80 | INFO     | module_stream.service.algorithm_config_service:list_available_algorithms:792 - 找到 3 个可用算法
2025-07-28 15:33:13.547 | afde54a38e5d4b9bb1f503990c302d80 | INFO     | module_stream.service.algorithm_config_service:get_algorithm_metadata_from_yaml:627 - 成功读取算法元数据: car_counting
2025-07-28 15:33:13.548 | afde54a38e5d4b9bb1f503990c302d80 | INFO     | module_stream.service.algorithm_config_service:get_algorithm_config_from_zhiquli:655 - 成功读取算法配置: car_counting
2025-07-28 15:33:13.548 | afde54a38e5d4b9bb1f503990c302d80 | INFO     | module_stream.service.algorithm_config_service:get_algorithm_config_from_zhiquli:655 - 成功读取算法配置: car_counting
2025-07-28 15:33:13.549 | afde54a38e5d4b9bb1f503990c302d80 | INFO     | module_stream.service.algorithm_config_service:get_algorithm_config_from_zhiquli:655 - 成功读取算法配置: car_counting
2025-07-28 15:33:13.549 | afde54a38e5d4b9bb1f503990c302d80 | INFO     | module_stream.service.algorithm_config_service:get_algorithm_metadata_from_yaml:627 - 成功读取算法元数据: person_counting
2025-07-28 15:33:13.550 | afde54a38e5d4b9bb1f503990c302d80 | INFO     | module_stream.service.algorithm_config_service:get_algorithm_config_from_zhiquli:655 - 成功读取算法配置: person_counting
2025-07-28 15:33:13.550 | afde54a38e5d4b9bb1f503990c302d80 | INFO     | module_stream.service.algorithm_config_service:get_algorithm_config_from_zhiquli:655 - 成功读取算法配置: person_counting
2025-07-28 15:33:13.551 | afde54a38e5d4b9bb1f503990c302d80 | INFO     | module_stream.service.algorithm_config_service:get_algorithm_config_from_zhiquli:655 - 成功读取算法配置: person_counting
2025-07-28 15:33:13.552 | afde54a38e5d4b9bb1f503990c302d80 | INFO     | module_stream.service.algorithm_config_service:get_algorithm_metadata_from_yaml:627 - 成功读取算法元数据: person_intrusion
2025-07-28 15:33:13.552 | afde54a38e5d4b9bb1f503990c302d80 | INFO     | module_stream.service.algorithm_config_service:get_algorithm_config_from_zhiquli:655 - 成功读取算法配置: person_intrusion
2025-07-28 15:33:13.553 | afde54a38e5d4b9bb1f503990c302d80 | INFO     | module_stream.service.algorithm_config_service:get_algorithm_config_from_zhiquli:655 - 成功读取算法配置: person_intrusion
2025-07-28 15:33:13.553 | afde54a38e5d4b9bb1f503990c302d80 | INFO     | module_stream.service.algorithm_config_service:get_algorithm_config_from_zhiquli:655 - 成功读取算法配置: person_intrusion
2025-07-28 15:33:13.553 | afde54a38e5d4b9bb1f503990c302d80 | INFO     | module_stream.controller.algorithm_standard_controller:get_algorithms_with_info:428 - 获取算法信息列表成功，共3个算法
2025-07-28 15:36:05.653 | 4ac8179e3aa74ecda6e8abc725945406 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 15:36:05.653 | 4ac8179e3aa74ecda6e8abc725945406 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 15:36:05.654 | 4ac8179e3aa74ecda6e8abc725945406 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 15:36:05.654 | 4ac8179e3aa74ecda6e8abc725945406 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 15:36:05.654 | 4ac8179e3aa74ecda6e8abc725945406 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 15:36:05.655 | 4ac8179e3aa74ecda6e8abc725945406 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 15:36:05.655 | 4ac8179e3aa74ecda6e8abc725945406 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 15:36:05.655 | 4ac8179e3aa74ecda6e8abc725945406 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 15:36:05.655 | 4ac8179e3aa74ecda6e8abc725945406 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 15:36:05.660 | 4ac8179e3aa74ecda6e8abc725945406 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-28 15:36:05.660 | 4ac8179e3aa74ecda6e8abc725945406 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 15:36:05.660 | 4ac8179e3aa74ecda6e8abc725945406 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-28 15:36:05.660 | 4ac8179e3aa74ecda6e8abc725945406 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-28 15:36:05.661 | 4ac8179e3aa74ecda6e8abc725945406 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 15:36:05.661 | 4ac8179e3aa74ecda6e8abc725945406 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-28 15:36:05.662 | 4ac8179e3aa74ecda6e8abc725945406 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 15:36:05.662 | 4ac8179e3aa74ecda6e8abc725945406 | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-28 15:36:11.745 | 96fd590eacf94fae9c95773267babf1f | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 15:36:11.746 | 96fd590eacf94fae9c95773267babf1f | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 15:36:11.746 | 96fd590eacf94fae9c95773267babf1f | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 15:36:11.746 | 96fd590eacf94fae9c95773267babf1f | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 15:36:11.746 | 96fd590eacf94fae9c95773267babf1f | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 15:36:11.747 | 96fd590eacf94fae9c95773267babf1f | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 15:36:11.747 | 96fd590eacf94fae9c95773267babf1f | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 15:36:11.747 | 96fd590eacf94fae9c95773267babf1f | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 15:36:11.747 | 96fd590eacf94fae9c95773267babf1f | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 15:36:11.748 | 96fd590eacf94fae9c95773267babf1f | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 15:36:11.761 | 96fd590eacf94fae9c95773267babf1f | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-28 15:36:11.762 | 96fd590eacf94fae9c95773267babf1f | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 15:36:11.762 | 96fd590eacf94fae9c95773267babf1f | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-28 15:36:11.762 | 96fd590eacf94fae9c95773267babf1f | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-28 15:36:11.766 | 96fd590eacf94fae9c95773267babf1f | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 15:36:11.767 | 96fd590eacf94fae9c95773267babf1f | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-28 15:36:11.767 | 96fd590eacf94fae9c95773267babf1f | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 15:36:11.767 | 96fd590eacf94fae9c95773267babf1f | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 15:39:38.257 | 2eb14d0b088b44a0b88475f601be3b30 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 15:39:40.146 | c53b58dc140045469c8fcb382a9558ca | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 15:39:43.942 | 7812479d4b0c4ec5baee2628fde70d67 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 15:50:41.487 | c6b1d367fd554f0bbd8b4d14f7728a2c | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 15:50:45.122 | f401418cd1ea498abf7f2095e8ac6e8f | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 15:51:00.899 | 83f52a0d7389410489661e09629f8ad1 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 15:51:08.414 | f969e3600166402a85ffb7fa93076373 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 15:57:05.859 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 15:57:05.860 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
