#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试检测框绘制修复效果
主要测试：
1. 框遗留问题修复（每次绘制使用新的frame副本）
2. 颜色问题修复（正确使用算法包提供的颜色）
"""

import cv2
import numpy as np
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'RuoYi-Vue3-FastAPI-master', 'ruoyi-fastapi-backend'))

from module_stream.service.task_execution_service import TaskExecutionService

def create_test_frame():
    """创建测试用的视频帧"""
    frame = np.zeros((600, 800, 3), dtype=np.uint8)
    # 添加一些背景内容
    cv2.rectangle(frame, (50, 50), (750, 550), (50, 50, 50), -1)
    cv2.putText(frame, "Test Frame", (300, 300), cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 2)
    return frame

def test_frame_copy_fix():
    """测试frame复制修复效果"""
    print("=== 测试frame复制修复效果 ===")
    
    # 创建原始帧
    original_frame = create_test_frame()
    print(f"原始帧ID: {id(original_frame)}")
    
    # 模拟算法包结果（包含颜色信息）
    alert_result_with_colors = {
        'hit': True,
        'message': '检测到告警目标',
        'details': {
            'detections': [
                {
                    'xyxy': [100, 100, 200, 200],
                    'conf': 0.85,
                    'name': 'car',
                    'ch_name': '车辆',
                    'track_id': 1,
                    'color': [255, 0, 0],  # 红色（RGB格式）
                    'alert_reason': '车辆进入禁止区域'
                },
                {
                    'xyxy': [300, 300, 400, 400],
                    'conf': 0.75,
                    'name': 'car',
                    'ch_name': '车辆',
                    'track_id': 2,
                    'color': [0, 255, 0],  # 绿色（RGB格式）
                }
            ],
            'configured_areas': [
                {
                    'name': '检测区域1',
                    'points': [
                        {'x': 0.1, 'y': 0.1},
                        {'x': 0.9, 'y': 0.1},
                        {'x': 0.9, 'y': 0.9},
                        {'x': 0.1, 'y': 0.9}
                    ]
                }
            ]
        }
    }
    
    # 第一次绘制
    print("\n第一次绘制:")
    frame1 = original_frame.copy()
    print(f"绘制前frame1 ID: {id(frame1)}")
    TaskExecutionService._draw_detection_boxes(frame1, alert_result_with_colors)
    print(f"绘制后frame1 ID: {id(frame1)}")
    
    # 第二次绘制（模拟车辆移动）
    print("\n第二次绘制（车辆移动）:")
    alert_result_moved = {
        'hit': False,
        'message': '检测到普通目标',
        'details': {
            'detections': [
                {
                    'xyxy': [500, 500, 600, 600],  # 车辆移动到新位置
                    'conf': 0.80,
                    'name': 'car',
                    'ch_name': '车辆',
                    'track_id': 3,
                    'color': [0, 255, 0],  # 绿色（RGB格式）
                }
            ],
            'configured_areas': [
                {
                    'name': '检测区域1',
                    'points': [
                        {'x': 0.1, 'y': 0.1},
                        {'x': 0.9, 'y': 0.1},
                        {'x': 0.9, 'y': 0.9},
                        {'x': 0.1, 'y': 0.9}
                    ]
                }
            ]
        }
    }
    
    frame2 = original_frame.copy()
    print(f"绘制前frame2 ID: {id(frame2)}")
    TaskExecutionService._draw_detection_boxes(frame2, alert_result_moved)
    print(f"绘制后frame2 ID: {id(frame2)}")
    
    # 保存结果图像
    cv2.imwrite('test_frame1_with_alert.jpg', frame1)
    cv2.imwrite('test_frame2_moved.jpg', frame2)
    cv2.imwrite('test_original_frame.jpg', original_frame)
    
    print(f"\n原始帧是否被修改: {not np.array_equal(original_frame, create_test_frame())}")
    print("测试图像已保存:")
    print("- test_original_frame.jpg: 原始帧")
    print("- test_frame1_with_alert.jpg: 第一次绘制（告警目标）")
    print("- test_frame2_moved.jpg: 第二次绘制（车辆移动）")

def test_color_handling():
    """测试颜色处理"""
    print("\n=== 测试颜色处理 ===")
    
    # 测试有颜色信息的情况
    print("1. 测试算法包提供颜色信息:")
    frame = create_test_frame()
    
    alert_result_with_colors = {
        'hit': True,
        'message': '测试颜色处理',
        'details': {
            'detections': [
                {
                    'xyxy': [50, 50, 150, 150],
                    'conf': 0.90,
                    'name': 'car',
                    'ch_name': '车辆',
                    'track_id': 1,
                    'color': [255, 0, 0],  # 红色（RGB）
                    'alert_reason': '告警：红色框'
                },
                {
                    'xyxy': [200, 50, 300, 150],
                    'conf': 0.85,
                    'name': 'car',
                    'ch_name': '车辆',
                    'track_id': 2,
                    'color': [0, 255, 0],  # 绿色（RGB）
                },
                {
                    'xyxy': [350, 50, 450, 150],
                    'conf': 0.80,
                    'name': 'car',
                    'ch_name': '车辆',
                    'track_id': 3,
                    'color': [0, 0, 255],  # 蓝色（RGB）
                    'alert_reason': '告警：蓝色框'
                }
            ]
        }
    }
    
    TaskExecutionService._draw_detection_boxes(frame, alert_result_with_colors)
    cv2.imwrite('test_colors_provided.jpg', frame)
    print("   保存: test_colors_provided.jpg")
    
    # 测试无颜色信息的情况（使用默认颜色）
    print("2. 测试无颜色信息（使用默认颜色）:")
    frame = create_test_frame()
    
    alert_result_no_colors = {
        'hit': True,
        'message': '测试默认颜色',
        'details': {
            'detections': [
                {
                    'xyxy': [50, 200, 150, 300],
                    'conf': 0.90,
                    'name': 'car',
                    'ch_name': '车辆',
                    'track_id': 4,
                    'alert_reason': '告警：默认红色框'
                },
                {
                    'xyxy': [200, 200, 300, 300],
                    'conf': 0.85,
                    'name': 'car',
                    'ch_name': '车辆',
                    'track_id': 5,
                    # 无color字段，无alert_reason -> 默认绿色
                }
            ]
        }
    }
    
    TaskExecutionService._draw_detection_boxes(frame, alert_result_no_colors)
    cv2.imwrite('test_colors_default.jpg', frame)
    print("   保存: test_colors_default.jpg")

if __name__ == "__main__":
    print("开始测试检测框绘制修复效果...")
    
    try:
        test_frame_copy_fix()
        test_color_handling()
        
        print("\n=== 测试完成 ===")
        print("请检查生成的图像文件，验证:")
        print("1. 框遗留问题是否修复（每次绘制都是全新的）")
        print("2. 颜色是否正确（告警红色，普通绿色，算法包指定颜色）")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
