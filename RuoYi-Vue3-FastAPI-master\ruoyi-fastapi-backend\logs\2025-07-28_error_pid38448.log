2025-07-28 16:47:18.506 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 16:47:18.506 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 16:47:18.537 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 16:47:18.537 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 16:47:18.538 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 16:47:18.569 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 16:47:18.594 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 16:47:18.594 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 16:47:22.729 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1293: 实体对象 = True
2025-07-28 16:47:22.730 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164633_561.jpg
2025-07-28 16:47:22.730 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164633_561.jpg
2025-07-28 16:47:22.737 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1293
2025-07-28 16:47:22.738 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1292: 实体对象 = True
2025-07-28 16:47:22.739 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164632_960.jpg
2025-07-28 16:47:22.739 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164632_960.jpg
2025-07-28 16:47:22.740 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1292
2025-07-28 16:47:22.742 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1291: 实体对象 = True
2025-07-28 16:47:22.742 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164632_219.jpg
2025-07-28 16:47:22.742 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164632_219.jpg
2025-07-28 16:47:22.744 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1291
2025-07-28 16:47:22.746 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1290: 实体对象 = True
2025-07-28 16:47:22.746 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164631_629.jpg
2025-07-28 16:47:22.746 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164631_629.jpg
2025-07-28 16:47:22.747 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1290
2025-07-28 16:47:22.748 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1289: 实体对象 = True
2025-07-28 16:47:22.749 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164630_908.jpg
2025-07-28 16:47:22.749 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164630_908.jpg
2025-07-28 16:47:22.750 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1289
2025-07-28 16:47:22.752 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1288: 实体对象 = True
2025-07-28 16:47:22.752 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164630_387.jpg
2025-07-28 16:47:22.752 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164630_387.jpg
2025-07-28 16:47:22.753 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1288
2025-07-28 16:47:22.755 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1287: 实体对象 = True
2025-07-28 16:47:22.755 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164629_899.jpg
2025-07-28 16:47:22.755 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164629_899.jpg
2025-07-28 16:47:22.757 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1287
2025-07-28 16:47:22.758 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1286: 实体对象 = True
2025-07-28 16:47:22.759 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164629_419.jpg
2025-07-28 16:47:22.759 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164629_419.jpg
2025-07-28 16:47:22.760 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1286
2025-07-28 16:47:22.761 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1285: 实体对象 = True
2025-07-28 16:47:22.761 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164628_781.jpg
2025-07-28 16:47:22.761 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164628_781.jpg
2025-07-28 16:47:22.764 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1285
2025-07-28 16:47:22.765 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1284: 实体对象 = True
2025-07-28 16:47:22.765 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164628_162.jpg
2025-07-28 16:47:22.766 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164628_162.jpg
2025-07-28 16:47:22.767 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1284
2025-07-28 16:47:22.771 | cd64547782104a14b3331a551f4ae056 | INFO     | module_alert.controller.alert_controller:delete_alert_manage_alert:118 - 成功删除10条记录，删除10个截图文件
2025-07-28 16:47:22.811 | 992a860fc2d546e7860eccdcca25cf21 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 16:47:24.981 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1283: 实体对象 = True
2025-07-28 16:47:24.981 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164627_702.jpg
2025-07-28 16:47:24.982 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164627_702.jpg
2025-07-28 16:47:24.987 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1283
2025-07-28 16:47:24.989 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1282: 实体对象 = True
2025-07-28 16:47:24.989 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164627_269.jpg
2025-07-28 16:47:24.990 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164627_269.jpg
2025-07-28 16:47:24.991 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1282
2025-07-28 16:47:24.992 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1281: 实体对象 = True
2025-07-28 16:47:24.993 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164626_832.jpg
2025-07-28 16:47:24.993 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164626_832.jpg
2025-07-28 16:47:24.994 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1281
2025-07-28 16:47:24.995 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1280: 实体对象 = True
2025-07-28 16:47:24.996 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164626_414.jpg
2025-07-28 16:47:24.996 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164626_414.jpg
2025-07-28 16:47:24.997 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1280
2025-07-28 16:47:24.998 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1279: 实体对象 = True
2025-07-28 16:47:24.999 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164626_012.jpg
2025-07-28 16:47:24.999 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164626_012.jpg
2025-07-28 16:47:25.000 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1279
2025-07-28 16:47:25.002 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1278: 实体对象 = True
2025-07-28 16:47:25.002 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164625_643.jpg
2025-07-28 16:47:25.002 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164625_643.jpg
2025-07-28 16:47:25.003 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1278
2025-07-28 16:47:25.005 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1277: 实体对象 = True
2025-07-28 16:47:25.005 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164625_143.jpg
2025-07-28 16:47:25.006 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164625_143.jpg
2025-07-28 16:47:25.007 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1277
2025-07-28 16:47:25.008 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1276: 实体对象 = True
2025-07-28 16:47:25.009 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164624_821.jpg
2025-07-28 16:47:25.009 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164624_821.jpg
2025-07-28 16:47:25.010 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1276
2025-07-28 16:47:25.011 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1275: 实体对象 = True
2025-07-28 16:47:25.012 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164624_513.jpg
2025-07-28 16:47:25.012 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164624_513.jpg
2025-07-28 16:47:25.013 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1275
2025-07-28 16:47:25.014 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1274: 实体对象 = True
2025-07-28 16:47:25.015 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164624_224.jpg
2025-07-28 16:47:25.015 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164624_224.jpg
2025-07-28 16:47:25.016 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1274
2025-07-28 16:47:25.019 | b12cc9d00a8040789beb44221f9a1164 | INFO     | module_alert.controller.alert_controller:delete_alert_manage_alert:118 - 成功删除10条记录，删除10个截图文件
2025-07-28 16:47:25.063 | 7a36acbc6e8e4069b72e9e5b3caf30de | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 16:47:28.154 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1273: 实体对象 = True
2025-07-28 16:47:28.155 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164623_944.jpg
2025-07-28 16:47:28.155 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164623_944.jpg
2025-07-28 16:47:28.157 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1273
2025-07-28 16:47:28.158 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1272: 实体对象 = True
2025-07-28 16:47:28.158 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164623_677.jpg
2025-07-28 16:47:28.159 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164623_677.jpg
2025-07-28 16:47:28.160 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1272
2025-07-28 16:47:28.160 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1271: 实体对象 = True
2025-07-28 16:47:28.161 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164623_261.jpg
2025-07-28 16:47:28.161 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164623_261.jpg
2025-07-28 16:47:28.163 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1271
2025-07-28 16:47:28.164 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1270: 实体对象 = True
2025-07-28 16:47:28.165 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164623_014.jpg
2025-07-28 16:47:28.165 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164623_014.jpg
2025-07-28 16:47:28.166 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1270
2025-07-28 16:47:28.167 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1269: 实体对象 = True
2025-07-28 16:47:28.167 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164622_782.jpg
2025-07-28 16:47:28.168 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164622_782.jpg
2025-07-28 16:47:28.169 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1269
2025-07-28 16:47:28.170 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1268: 实体对象 = True
2025-07-28 16:47:28.171 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164622_000.jpg
2025-07-28 16:47:28.171 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164622_000.jpg
2025-07-28 16:47:28.172 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1268
2025-07-28 16:47:28.173 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1267: 实体对象 = True
2025-07-28 16:47:28.174 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164621_783.jpg
2025-07-28 16:47:28.174 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164621_783.jpg
2025-07-28 16:47:28.175 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1267
2025-07-28 16:47:28.176 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1266: 实体对象 = True
2025-07-28 16:47:28.177 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164621_566.jpg
2025-07-28 16:47:28.177 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164621_566.jpg
2025-07-28 16:47:28.178 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1266
2025-07-28 16:47:28.179 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1265: 实体对象 = True
2025-07-28 16:47:28.180 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164621_350.jpg
2025-07-28 16:47:28.180 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164621_350.jpg
2025-07-28 16:47:28.181 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1265
2025-07-28 16:47:28.182 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1264: 实体对象 = True
2025-07-28 16:47:28.183 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164621_122.jpg
2025-07-28 16:47:28.183 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164621_122.jpg
2025-07-28 16:47:28.184 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1264
2025-07-28 16:47:28.187 | e9979a3218cd42389739b7fd66fd85d6 | INFO     | module_alert.controller.alert_controller:delete_alert_manage_alert:118 - 成功删除10条记录，删除10个截图文件
2025-07-28 16:47:28.222 | 11fdb0b40ea544228a1823dcd8f82957 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 16:47:30.472 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1263: 实体对象 = True
2025-07-28 16:47:30.473 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164619_253.jpg
2025-07-28 16:47:30.473 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164619_253.jpg
2025-07-28 16:47:30.474 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1263
2025-07-28 16:47:30.475 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1262: 实体对象 = True
2025-07-28 16:47:30.476 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164619_043.jpg
2025-07-28 16:47:30.476 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164619_043.jpg
2025-07-28 16:47:30.477 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1262
2025-07-28 16:47:30.478 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1261: 实体对象 = True
2025-07-28 16:47:30.479 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164618_842.jpg
2025-07-28 16:47:30.479 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164618_842.jpg
2025-07-28 16:47:30.480 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1261
2025-07-28 16:47:30.481 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1260: 实体对象 = True
2025-07-28 16:47:30.482 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164618_610.jpg
2025-07-28 16:47:30.482 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164618_610.jpg
2025-07-28 16:47:30.483 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1260
2025-07-28 16:47:30.484 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1259: 实体对象 = True
2025-07-28 16:47:30.485 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_164618_405.jpg
2025-07-28 16:47:30.485 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_164618_405.jpg
2025-07-28 16:47:30.486 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1259
2025-07-28 16:47:30.488 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1178: 实体对象 = True
2025-07-28 16:47:30.488 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_162004_723.jpg
2025-07-28 16:47:30.488 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_162004_723.jpg
2025-07-28 16:47:30.490 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1178
2025-07-28 16:47:30.491 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1177: 实体对象 = True
2025-07-28 16:47:30.492 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_162004_203.jpg
2025-07-28 16:47:30.492 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_162004_203.jpg
2025-07-28 16:47:30.493 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1177
2025-07-28 16:47:30.494 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1176: 实体对象 = True
2025-07-28 16:47:30.495 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_162003_675.jpg
2025-07-28 16:47:30.495 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_162003_675.jpg
2025-07-28 16:47:30.496 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1176
2025-07-28 16:47:30.498 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1175: 实体对象 = True
2025-07-28 16:47:30.498 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_162003_134.jpg
2025-07-28 16:47:30.498 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_162003_134.jpg
2025-07-28 16:47:30.499 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1175
2025-07-28 16:47:30.501 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1174: 实体对象 = True
2025-07-28 16:47:30.501 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_162002_606.jpg
2025-07-28 16:47:30.502 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_162002_606.jpg
2025-07-28 16:47:30.503 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1174
2025-07-28 16:47:30.507 | b97790224e1642e388e430351f29dfd6 | INFO     | module_alert.controller.alert_controller:delete_alert_manage_alert:118 - 成功删除10条记录，删除10个截图文件
2025-07-28 16:47:30.537 | a8d48506bff549f396274311045714f5 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 16:47:33.083 | 1caf23dedfb043118f157fb5f9ddb27a | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1173: 实体对象 = True
2025-07-28 16:47:33.083 | 1caf23dedfb043118f157fb5f9ddb27a | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_162002_092.jpg
2025-07-28 16:47:33.084 | 1caf23dedfb043118f157fb5f9ddb27a | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_162002_092.jpg
2025-07-28 16:47:33.085 | 1caf23dedfb043118f157fb5f9ddb27a | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1173
2025-07-28 16:47:33.086 | 1caf23dedfb043118f157fb5f9ddb27a | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1172: 实体对象 = True
2025-07-28 16:47:33.087 | 1caf23dedfb043118f157fb5f9ddb27a | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_162001_594.jpg
2025-07-28 16:47:33.087 | 1caf23dedfb043118f157fb5f9ddb27a | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_162001_594.jpg
2025-07-28 16:47:33.088 | 1caf23dedfb043118f157fb5f9ddb27a | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1172
2025-07-28 16:47:33.089 | 1caf23dedfb043118f157fb5f9ddb27a | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1171: 实体对象 = True
2025-07-28 16:47:33.090 | 1caf23dedfb043118f157fb5f9ddb27a | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_162001_133.jpg
2025-07-28 16:47:33.090 | 1caf23dedfb043118f157fb5f9ddb27a | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_162001_133.jpg
2025-07-28 16:47:33.091 | 1caf23dedfb043118f157fb5f9ddb27a | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1171
2025-07-28 16:47:33.092 | 1caf23dedfb043118f157fb5f9ddb27a | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1170: 实体对象 = True
2025-07-28 16:47:33.093 | 1caf23dedfb043118f157fb5f9ddb27a | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_162000_693.jpg
2025-07-28 16:47:33.093 | 1caf23dedfb043118f157fb5f9ddb27a | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_162000_693.jpg
2025-07-28 16:47:33.094 | 1caf23dedfb043118f157fb5f9ddb27a | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1170
2025-07-28 16:47:33.096 | 1caf23dedfb043118f157fb5f9ddb27a | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1169: 实体对象 = True
2025-07-28 16:47:33.096 | 1caf23dedfb043118f157fb5f9ddb27a | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_162000_406.jpg
2025-07-28 16:47:33.096 | 1caf23dedfb043118f157fb5f9ddb27a | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_162000_406.jpg
2025-07-28 16:47:33.097 | 1caf23dedfb043118f157fb5f9ddb27a | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1169
2025-07-28 16:47:33.099 | 1caf23dedfb043118f157fb5f9ddb27a | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1108: 实体对象 = True
2025-07-28 16:47:33.099 | 1caf23dedfb043118f157fb5f9ddb27a | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_152635_469.jpg
2025-07-28 16:47:33.099 | 1caf23dedfb043118f157fb5f9ddb27a | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_152635_469.jpg
2025-07-28 16:47:33.100 | 1caf23dedfb043118f157fb5f9ddb27a | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1108
2025-07-28 16:47:33.104 | 1caf23dedfb043118f157fb5f9ddb27a | INFO     | module_alert.controller.alert_controller:delete_alert_manage_alert:118 - 成功删除6条记录，删除6个截图文件
2025-07-28 16:47:33.133 | 70f979db3c1340a28b4ecd0bda37cef4 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 16:47:36.195 | e5844ce37e6f4359bd23817b6c7470e7 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 16:47:36.195 | e5844ce37e6f4359bd23817b6c7470e7 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 16:47:36.196 | e5844ce37e6f4359bd23817b6c7470e7 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 16:47:36.196 | e5844ce37e6f4359bd23817b6c7470e7 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 16:47:36.196 | e5844ce37e6f4359bd23817b6c7470e7 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 16:47:36.196 | e5844ce37e6f4359bd23817b6c7470e7 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 16:47:36.196 | e5844ce37e6f4359bd23817b6c7470e7 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 16:47:36.197 | e5844ce37e6f4359bd23817b6c7470e7 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 16:47:36.197 | e5844ce37e6f4359bd23817b6c7470e7 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 16:47:36.198 | e5844ce37e6f4359bd23817b6c7470e7 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 16:47:36.203 | e5844ce37e6f4359bd23817b6c7470e7 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-28 16:47:36.203 | e5844ce37e6f4359bd23817b6c7470e7 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 16:47:36.203 | e5844ce37e6f4359bd23817b6c7470e7 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-28 16:47:36.203 | e5844ce37e6f4359bd23817b6c7470e7 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-28 16:47:36.205 | e5844ce37e6f4359bd23817b6c7470e7 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 16:47:36.206 | e5844ce37e6f4359bd23817b6c7470e7 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-28 16:47:36.206 | e5844ce37e6f4359bd23817b6c7470e7 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 16:47:36.206 | e5844ce37e6f4359bd23817b6c7470e7 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 16:47:38.122 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:start_task:204 - 开始启动任务: 12
2025-07-28 16:47:38.125 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:498 - 加载算法配置: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info']
2025-07-28 16:47:38.125 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:505 - 加载检测区域配置: ['version', 'created_at', 'detection_areas', 'detection_lines', 'exclusion_areas']
2025-07-28 16:47:38.125 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:512 - 加载告警配置: ['version', 'created_at', 'alert_params']
2025-07-28 16:47:38.125 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:519 - 加载用户配置（优先级最高）: ['algorithm_id', 'custom_params', 'algorithm_name', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-28 16:47:38.126 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:525 - 成功加载数据库配置，包含字段: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_params', 'algorithm_id', 'algorithm_name', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-28 16:47:38.126 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:526 - 配置加载优先级: algorithm_config < bbox_config < alert_config < user_config
2025-07-28 16:47:38.126 | cbf715d4efa045eeb6c32c0fbb858670 | WARNING  | module_stream.service.task_execution_service:_validate_required_config:640 - 算法 car_counting 缺少配置参数: 置信度阈值 (confidence_threshold 或 conf_thres), NMS阈值 (nms_threshold 或 nms_thres), 输入图像尺寸 (input_size 或 img_size)。将使用默认值，建议在算法配置页面设置这些参数以获得更好的检测效果。
2025-07-28 16:47:38.126 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:664 - 验证模型初始化 - 添加YOLOv5路径: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master
2025-07-28 16:47:38.127 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:679 - 验证模型初始化 - 成功预导入YOLOv5 utils模块
2025-07-28 16:47:38.127 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:684 - 验证模型初始化 - 当前sys.path前5项: ['D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master\\utils', 'D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master', 'D:\\ai-recognition\\RuoYi-Vue3-FastAPI-master\\ruoyi-fastapi-backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs']
2025-07-28 16:47:38.128 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:685 - 验证模型初始化 - 当前工作目录: D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend
2025-07-28 16:47:38.128 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:689 - 验证模型初始化 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-28 16:47:39.396 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:710 - 验证模型初始化 - 成功导入智驱力模型
2025-07-28 16:47:39.396 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:47:39.396 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:47:39.396 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:47:39.396 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:478 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-28 16:47:39.396 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:722 - 验证模型初始化 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-28 16:47:41.706 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:734 - 验证模型初始化 - 智驱力模型初始化成功
2025-07-28 16:47:41.706 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:735 -    - 设备: cuda
2025-07-28 16:47:41.706 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:736 -    - 图像尺寸: 640
2025-07-28 16:47:41.707 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:737 -    - 置信度阈值: 0.01
2025-07-28 16:47:41.707 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:738 -    - NMS阈值: 0.5
2025-07-28 16:47:41.712 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:751 - 验证模型初始化 - 智驱力后处理器初始化成功
2025-07-28 16:47:41.727 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:start_monitor_stream:3858 - 任务 12 的监控流已启动
2025-07-28 16:47:41.729 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:_cache_task_config:112 - 任务12配置已缓存
2025-07-28 16:47:41.729 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:detection_loop:799 - 任务12配置缓存完成: 区域1个, 线段0个
2025-07-28 16:47:41.729 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:detection_loop:829 - 成功预导入YOLOv5 utils模块
2025-07-28 16:47:41.730 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:detection_loop:835 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-28 16:47:41.730 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 重新加载模块: zql_detect
2025-07-28 16:47:41.731 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 重新加载模块: model
2025-07-28 16:47:41.731 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:detection_loop:855 - 成功导入智驱力模型
2025-07-28 16:47:41.731 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:47:41.731 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:47:41.731 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:47:41.731 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:478 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-28 16:47:41.731 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:detection_loop:867 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-28 16:47:41.855 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:detection_loop:880 - 智驱力模型初始化成功
2025-07-28 16:47:41.855 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:detection_loop:881 -    - 设备: cuda
2025-07-28 16:47:41.855 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:detection_loop:882 -    - 图像尺寸: 640
2025-07-28 16:47:41.856 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:detection_loop:883 -    - 置信度阈值: 0.01
2025-07-28 16:47:41.856 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:detection_loop:884 -    - NMS阈值: 0.5
2025-07-28 16:47:41.856 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:detection_loop:897 - 智驱力后处理器初始化成功
2025-07-28 16:48:11.943 | cbf715d4efa045eeb6c32c0fbb858670 | ERROR    | module_stream.service.task_execution_service:detection_loop:917 - 视频流连接超时或失败: rtsp://127.0.0.1:8554/test1
2025-07-28 16:48:11.947 | cbf715d4efa045eeb6c32c0fbb858670 | ERROR    | module_stream.service.task_execution_service:_update_task_status_with_error:3288 - 更新任务状态失败: readexactly() called while another coroutine is already waiting for incoming data
2025-07-28 16:48:11.947 | cbf715d4efa045eeb6c32c0fbb858670 | ERROR    | module_stream.service.task_execution_service:detection_loop:1270 - 检测过程中发生错误: Method 'rollback()' can't be called here; method 'commit()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-07-28 16:48:11.947 | cbf715d4efa045eeb6c32c0fbb858670 | ERROR    | module_stream.service.task_execution_service:_update_task_status_with_error:3288 - 更新任务状态失败: This session is in 'prepared' state; no further SQL can be emitted within this transaction.
2025-07-28 16:48:11.949 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.service.task_execution_service:start_task:255 - 任务 12 启动成功，包括实时监控流
2025-07-28 16:48:11.949 | cbf715d4efa045eeb6c32c0fbb858670 | INFO     | module_stream.controller.monitor_controller:batch_start_tasks:157 - 批量启动任务完全成功: [12]
2025-07-28 16:48:11.960 | 32ab10a00e954782815157fb6fd2ab3c | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 16:48:11.960 | 32ab10a00e954782815157fb6fd2ab3c | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 16:48:11.960 | 32ab10a00e954782815157fb6fd2ab3c | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 16:48:11.960 | 32ab10a00e954782815157fb6fd2ab3c | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 16:48:11.960 | 32ab10a00e954782815157fb6fd2ab3c | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 16:48:11.961 | 32ab10a00e954782815157fb6fd2ab3c | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 16:48:11.961 | 32ab10a00e954782815157fb6fd2ab3c | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 16:48:11.961 | 32ab10a00e954782815157fb6fd2ab3c | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 16:48:11.961 | 32ab10a00e954782815157fb6fd2ab3c | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 16:48:11.965 | 32ab10a00e954782815157fb6fd2ab3c | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-28 16:48:11.965 | 32ab10a00e954782815157fb6fd2ab3c | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 16:48:11.965 | 32ab10a00e954782815157fb6fd2ab3c | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-28 16:48:11.965 | 32ab10a00e954782815157fb6fd2ab3c | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-28 16:48:11.966 | 32ab10a00e954782815157fb6fd2ab3c | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 16:48:11.967 | 32ab10a00e954782815157fb6fd2ab3c | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-28 16:48:11.967 | 32ab10a00e954782815157fb6fd2ab3c | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 16:48:11.967 | 32ab10a00e954782815157fb6fd2ab3c | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-28 16:48:11.973 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:52 - 开始WebSocket连接: 任务12 (无认证模式)
2025-07-28 16:48:11.974 |  | INFO     | module_stream.service.task_execution_service:add_monitor_client:3941 - 客户端 feaa455d-3bf1-4cb7-8c37-2528af0675ef 已连接到任务 12 的监控流
2025-07-28 16:48:11.974 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:73 - 客户端 feaa455d-3bf1-4cb7-8c37-2528af0675ef 连接到任务 12 的监控流 (无认证模式)
2025-07-28 16:48:14.135 | b0c74aa110e142f8896ed3698995d2c8 | ERROR    | exceptions.handle:exception_handler:117 - 数据库连接错误: (asyncmy.errors.OperationalError) (2014, 'Command Out of Sync')
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-28 16:48:14.264 | f8bb4780c7fa41fcbea18c3653935202 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 16:48:14.264 | f8bb4780c7fa41fcbea18c3653935202 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 16:48:14.264 | f8bb4780c7fa41fcbea18c3653935202 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 16:48:14.264 | f8bb4780c7fa41fcbea18c3653935202 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 16:48:14.265 | f8bb4780c7fa41fcbea18c3653935202 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 16:48:14.265 | f8bb4780c7fa41fcbea18c3653935202 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 16:48:14.265 | f8bb4780c7fa41fcbea18c3653935202 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 16:48:14.265 | f8bb4780c7fa41fcbea18c3653935202 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 16:48:14.266 | f8bb4780c7fa41fcbea18c3653935202 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 16:48:14.268 | f8bb4780c7fa41fcbea18c3653935202 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-28 16:48:14.269 | f8bb4780c7fa41fcbea18c3653935202 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 16:48:14.269 | f8bb4780c7fa41fcbea18c3653935202 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-28 16:48:14.269 | f8bb4780c7fa41fcbea18c3653935202 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-28 16:48:14.270 | f8bb4780c7fa41fcbea18c3653935202 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 16:48:14.270 | f8bb4780c7fa41fcbea18c3653935202 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-28 16:48:14.270 | f8bb4780c7fa41fcbea18c3653935202 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 16:48:14.270 | f8bb4780c7fa41fcbea18c3653935202 | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-28 16:48:16.008 | 32d8ec7c6ae54e68b081d7046b184e44 | INFO     | module_stream.service.task_execution_service:stop_task:306 - 开始停止任务: 12
2025-07-28 16:48:16.010 | 32d8ec7c6ae54e68b081d7046b184e44 | INFO     | module_stream.service.task_execution_service:stop_task:330 - 已从运行任务列表移除: 12
2025-07-28 16:48:16.010 | 32d8ec7c6ae54e68b081d7046b184e44 | INFO     | module_stream.service.task_execution_service:stop_task:339 - 任务 12 使用智驱力直接集成，无需清理外部进程
2025-07-28 16:48:16.010 | 32d8ec7c6ae54e68b081d7046b184e44 | INFO     | module_stream.service.task_execution_service:stop_monitor_stream:3882 - 任务 12 的监控流已停止
2025-07-28 16:48:16.010 | 32d8ec7c6ae54e68b081d7046b184e44 | INFO     | module_stream.service.task_execution_service:stop_task:344 - 监控流停止成功: 12
2025-07-28 16:48:16.010 | 32d8ec7c6ae54e68b081d7046b184e44 | INFO     | module_stream.service.task_execution_service:_clear_task_cache:133 - 任务12缓存已清除
2025-07-28 16:48:16.011 | 32d8ec7c6ae54e68b081d7046b184e44 | INFO     | module_stream.service.task_execution_service:stop_task:353 - 任务缓存清理成功: 12
2025-07-28 16:48:16.020 | 32d8ec7c6ae54e68b081d7046b184e44 | INFO     | module_stream.service.task_execution_service:stop_task:362 - 任务状态更新成功: 12
2025-07-28 16:48:16.020 | 32d8ec7c6ae54e68b081d7046b184e44 | INFO     | module_stream.service.task_execution_service:stop_task:374 - 任务 12 停止成功，包括实时监控流
2025-07-28 16:48:16.037 | 32d8ec7c6ae54e68b081d7046b184e44 | INFO     | module_stream.controller.monitor_controller:batch_stop_tasks:209 - 批量停止任务成功: [12]
2025-07-28 16:48:16.085 |  | INFO     | module_stream.controller.monitor_websocket_controller:_start_stream_push:180 - 客户端 feaa455d-3bf1-4cb7-8c37-2528af0675ef 主动断开连接
2025-07-28 16:48:16.086 |  | INFO     | module_stream.controller.monitor_websocket_controller:_cleanup_connection:209 - 客户端 feaa455d-3bf1-4cb7-8c37-2528af0675ef 连接已清理
2025-07-28 16:48:42.532 | 4ac7d8166c2545c4b784541387103db0 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 16:48:42.547 | e0054f657ddd4fadb0aa4b0430d87014 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 16:48:42.652 | a80623eef7b940028d77df97c5c45f05 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 16:48:42.652 | a80623eef7b940028d77df97c5c45f05 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 16:48:42.653 | a80623eef7b940028d77df97c5c45f05 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 16:48:42.653 | a80623eef7b940028d77df97c5c45f05 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 16:48:42.654 | a80623eef7b940028d77df97c5c45f05 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 16:48:42.655 | a80623eef7b940028d77df97c5c45f05 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 16:48:42.655 | a80623eef7b940028d77df97c5c45f05 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 16:48:42.655 | a80623eef7b940028d77df97c5c45f05 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 16:48:42.655 | a80623eef7b940028d77df97c5c45f05 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 16:48:42.658 | a80623eef7b940028d77df97c5c45f05 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-28 16:48:42.659 | a80623eef7b940028d77df97c5c45f05 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 16:48:42.659 | a80623eef7b940028d77df97c5c45f05 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-28 16:48:42.659 | a80623eef7b940028d77df97c5c45f05 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-28 16:48:42.661 | a80623eef7b940028d77df97c5c45f05 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 16:48:42.661 | a80623eef7b940028d77df97c5c45f05 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-28 16:48:42.662 | a80623eef7b940028d77df97c5c45f05 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 16:48:42.662 | a80623eef7b940028d77df97c5c45f05 | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-28 16:48:44.214 | 13303d708cd144f7a260d8132eeb5e85 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 16:48:44.214 | 13303d708cd144f7a260d8132eeb5e85 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 16:48:44.215 | 13303d708cd144f7a260d8132eeb5e85 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 16:48:44.215 | 13303d708cd144f7a260d8132eeb5e85 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 16:48:44.215 | 13303d708cd144f7a260d8132eeb5e85 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 16:48:44.215 | 13303d708cd144f7a260d8132eeb5e85 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 16:48:44.215 | 13303d708cd144f7a260d8132eeb5e85 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 16:48:44.216 | 13303d708cd144f7a260d8132eeb5e85 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 16:48:44.216 | 13303d708cd144f7a260d8132eeb5e85 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 16:48:44.216 | 13303d708cd144f7a260d8132eeb5e85 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 16:48:44.219 | 13303d708cd144f7a260d8132eeb5e85 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-28 16:48:44.219 | 13303d708cd144f7a260d8132eeb5e85 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 16:48:44.220 | 13303d708cd144f7a260d8132eeb5e85 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-28 16:48:44.220 | 13303d708cd144f7a260d8132eeb5e85 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-28 16:48:44.221 | 13303d708cd144f7a260d8132eeb5e85 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 16:48:44.221 | 13303d708cd144f7a260d8132eeb5e85 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-28 16:48:44.221 | 13303d708cd144f7a260d8132eeb5e85 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 16:48:44.221 | 13303d708cd144f7a260d8132eeb5e85 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 16:48:45.883 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:start_task:204 - 开始启动任务: 12
2025-07-28 16:48:45.886 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:498 - 加载算法配置: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info']
2025-07-28 16:48:45.886 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:505 - 加载检测区域配置: ['version', 'created_at', 'detection_areas', 'detection_lines', 'exclusion_areas']
2025-07-28 16:48:45.886 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:512 - 加载告警配置: ['version', 'created_at', 'alert_params']
2025-07-28 16:48:45.887 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:519 - 加载用户配置（优先级最高）: ['algorithm_id', 'custom_params', 'algorithm_name', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-28 16:48:45.887 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:525 - 成功加载数据库配置，包含字段: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_params', 'algorithm_id', 'algorithm_name', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-28 16:48:45.887 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:526 - 配置加载优先级: algorithm_config < bbox_config < alert_config < user_config
2025-07-28 16:48:45.888 | be40b71884184b3db68a19d5057154ca | WARNING  | module_stream.service.task_execution_service:_validate_required_config:640 - 算法 car_counting 缺少配置参数: 置信度阈值 (confidence_threshold 或 conf_thres), NMS阈值 (nms_threshold 或 nms_thres), 输入图像尺寸 (input_size 或 img_size)。将使用默认值，建议在算法配置页面设置这些参数以获得更好的检测效果。
2025-07-28 16:48:45.888 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:679 - 验证模型初始化 - 成功预导入YOLOv5 utils模块
2025-07-28 16:48:45.889 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:684 - 验证模型初始化 - 当前sys.path前5项: ['D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master', 'D:\\ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting\\model', 'D:\\ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting\\postprocessor', 'D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting\\postprocessor', 'D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master']
2025-07-28 16:48:45.889 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:685 - 验证模型初始化 - 当前工作目录: D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend
2025-07-28 16:48:45.889 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:689 - 验证模型初始化 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-28 16:48:45.890 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:706 - 验证模型初始化 - 重新加载模块: zql_detect
2025-07-28 16:48:45.891 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:706 - 验证模型初始化 - 重新加载模块: model
2025-07-28 16:48:45.891 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:710 - 验证模型初始化 - 成功导入智驱力模型
2025-07-28 16:48:45.891 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:48:45.891 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:48:45.891 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:48:45.891 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:478 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-28 16:48:45.891 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:722 - 验证模型初始化 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-28 16:48:46.038 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:734 - 验证模型初始化 - 智驱力模型初始化成功
2025-07-28 16:48:46.039 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:735 -    - 设备: cuda
2025-07-28 16:48:46.039 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:736 -    - 图像尺寸: 640
2025-07-28 16:48:46.039 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:737 -    - 置信度阈值: 0.01
2025-07-28 16:48:46.039 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:738 -    - NMS阈值: 0.5
2025-07-28 16:48:46.039 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:751 - 验证模型初始化 - 智驱力后处理器初始化成功
2025-07-28 16:48:46.052 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:start_monitor_stream:3858 - 任务 12 的监控流已启动
2025-07-28 16:48:46.054 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_cache_task_config:112 - 任务12配置已缓存
2025-07-28 16:48:46.054 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:799 - 任务12配置缓存完成: 区域1个, 线段0个
2025-07-28 16:48:46.054 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:829 - 成功预导入YOLOv5 utils模块
2025-07-28 16:48:46.055 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:835 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-28 16:48:46.055 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 重新加载模块: zql_detect
2025-07-28 16:48:46.056 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 重新加载模块: model
2025-07-28 16:48:46.056 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:855 - 成功导入智驱力模型
2025-07-28 16:48:46.056 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:48:46.056 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:48:46.056 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:48:46.057 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:478 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-28 16:48:46.057 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:867 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-28 16:48:46.188 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:880 - 智驱力模型初始化成功
2025-07-28 16:48:46.189 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:881 -    - 设备: cuda
2025-07-28 16:48:46.189 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:882 -    - 图像尺寸: 640
2025-07-28 16:48:46.189 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:883 -    - 置信度阈值: 0.01
2025-07-28 16:48:46.189 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:884 -    - NMS阈值: 0.5
2025-07-28 16:48:46.189 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:897 - 智驱力后处理器初始化成功
2025-07-28 16:49:07.626 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:07.626 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:07.626 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:07.626 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:07.814 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:07.814 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 4
2025-07-28 16:49:07.814 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}
2025-07-28 16:49:07.814 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 329, 188]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 16:49:07.814 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:07.814 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:07.815 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:07.815 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:07.815 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:07.815 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:07.815 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:07.816 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:07.816 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:07.816 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:07.816 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:07.817 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:07.817 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:07.820 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:07.820 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:07.820 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:07.820 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 2 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:07.820 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:07.821 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2], 'unique_vehicle_ids': [1, 2], 'vehicle_count': 2, 'unique_vehicle_count': 2}
2025-07-28 16:49:07.821 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2]
2025-07-28 16:49:07.821 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2]
2025-07-28 16:49:07.821 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 2
2025-07-28 16:49:07.822 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 190, 320, 251]
2025-07-28 16:49:07.822 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 149, 329, 188]
2025-07-28 16:49:07.822 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 2 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 329, 188], 'track_id': 2, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:07.820505', 'vehicle_tracking': {'vehicle_ids': [1, 2], 'unique_vehicle_ids': [1, 2], 'vehicle_count': 2, 'unique_vehicle_count': 2}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692547.8205137}
2025-07-28 16:49:07.832 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:start_task:255 - 任务 12 启动成功，包括实时监控流
2025-07-28 16:49:07.832 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.controller.monitor_controller:batch_start_tasks:157 - 批量启动任务完全成功: [12]
2025-07-28 16:49:07.833 | fd88043f29614c9390c038e18a52abbe | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 16:49:07.833 | fd88043f29614c9390c038e18a52abbe | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 16:49:07.834 | fd88043f29614c9390c038e18a52abbe | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 16:49:07.834 | fd88043f29614c9390c038e18a52abbe | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 16:49:07.834 | fd88043f29614c9390c038e18a52abbe | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 16:49:07.834 | fd88043f29614c9390c038e18a52abbe | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 16:49:07.834 | fd88043f29614c9390c038e18a52abbe | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 16:49:07.834 | fd88043f29614c9390c038e18a52abbe | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 16:49:07.835 | fd88043f29614c9390c038e18a52abbe | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 16:49:07.838 | fd88043f29614c9390c038e18a52abbe | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-28 16:49:07.839 | fd88043f29614c9390c038e18a52abbe | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 16:49:07.839 | fd88043f29614c9390c038e18a52abbe | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-28 16:49:07.839 | fd88043f29614c9390c038e18a52abbe | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-28 16:49:07.840 | fd88043f29614c9390c038e18a52abbe | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 16:49:07.840 | fd88043f29614c9390c038e18a52abbe | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-28 16:49:07.841 | fd88043f29614c9390c038e18a52abbe | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 16:49:07.841 | fd88043f29614c9390c038e18a52abbe | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-28 16:49:07.851 | 9db958e06dab4bb69d34c87f70f594f9 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 16:49:07.851 | 9db958e06dab4bb69d34c87f70f594f9 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 16:49:07.851 | 9db958e06dab4bb69d34c87f70f594f9 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 16:49:07.851 | 9db958e06dab4bb69d34c87f70f594f9 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 16:49:07.852 | 9db958e06dab4bb69d34c87f70f594f9 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 16:49:07.852 | 9db958e06dab4bb69d34c87f70f594f9 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 16:49:07.852 | 9db958e06dab4bb69d34c87f70f594f9 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 16:49:07.852 | 9db958e06dab4bb69d34c87f70f594f9 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 16:49:07.852 | 9db958e06dab4bb69d34c87f70f594f9 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 16:49:07.853 | 9db958e06dab4bb69d34c87f70f594f9 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 16:49:07.856 | 9db958e06dab4bb69d34c87f70f594f9 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-28 16:49:07.857 | 9db958e06dab4bb69d34c87f70f594f9 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 16:49:07.857 | 9db958e06dab4bb69d34c87f70f594f9 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-28 16:49:07.857 | 9db958e06dab4bb69d34c87f70f594f9 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-28 16:49:07.858 | 9db958e06dab4bb69d34c87f70f594f9 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 16:49:07.858 | 9db958e06dab4bb69d34c87f70f594f9 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-28 16:49:07.858 | 9db958e06dab4bb69d34c87f70f594f9 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 16:49:07.859 | 9db958e06dab4bb69d34c87f70f594f9 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 16:49:07.938 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:07.939 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:07.939 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:07.939 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:07.958 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:07.959 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 4
2025-07-28 16:49:07.959 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 16:49:07.959 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 329, 187]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 16:49:07.959 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:07.959 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:07.959 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:07.959 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:07.960 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:07.960 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:07.960 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:07.960 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:07.961 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:07.961 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:07.961 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:07.961 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:07.961 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:07.961 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:07.965 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:07.965 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:07.965 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:07.965 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 4 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:07.965 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:07.965 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4], 'unique_vehicle_ids': [1, 2, 3, 4], 'vehicle_count': 4, 'unique_vehicle_count': 4}
2025-07-28 16:49:07.966 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4]
2025-07-28 16:49:07.966 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4]
2025-07-28 16:49:07.966 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 4
2025-07-28 16:49:07.966 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 190, 321, 251]
2025-07-28 16:49:07.967 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 149, 329, 187]
2025-07-28 16:49:07.967 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 134, 355, 162]
2025-07-28 16:49:07.967 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 4 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 329, 187], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.29, 'name': 'car', 'ch_name': 'car', 'xyxy': [514, 70, 536, 91], 'track_id': 4, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:07.965420', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4], 'unique_vehicle_ids': [1, 2, 3, 4], 'vehicle_count': 4, 'unique_vehicle_count': 4}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692547.9654274}
2025-07-28 16:49:08.078 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:08.078 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:08.079 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:08.079 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:08.136 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:08.137 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 4
2025-07-28 16:49:08.137 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 16:49:08.137 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 329, 188]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 356, 162]}]
2025-07-28 16:49:08.137 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:08.137 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:08.138 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:08.138 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:08.138 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:08.138 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:08.139 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:08.139 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:08.139 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:08.140 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:08.140 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:08.140 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:08.140 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:08.140 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:08.143 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:08.143 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:08.143 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:08.143 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 4 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:08.143 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:08.144 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4], 'unique_vehicle_ids': [1, 2, 3, 4], 'vehicle_count': 4, 'unique_vehicle_count': 4}
2025-07-28 16:49:08.144 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4]
2025-07-28 16:49:08.144 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4]
2025-07-28 16:49:08.144 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 4
2025-07-28 16:49:08.144 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 190, 321, 251]
2025-07-28 16:49:08.145 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 149, 329, 188]
2025-07-28 16:49:08.145 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 134, 356, 162]
2025-07-28 16:49:08.145 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 4 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 329, 188], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 356, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [513, 69, 536, 91], 'track_id': 4, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:08.143397', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4], 'unique_vehicle_ids': [1, 2, 3, 4], 'vehicle_count': 4, 'unique_vehicle_count': 4}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692548.1434035}
2025-07-28 16:49:08.146 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=True, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:08.146 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:08.146 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 4, 配置区域数量: 0
2025-07-28 16:49:08.146 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:08.147 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:08.147 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:08.147 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:08.147 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3811 - 绘制完成: 绘制4个检测框, 跳过0个
2025-07-28 16:49:08.148 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3704 - 任务 12 队列满，替换旧帧
2025-07-28 16:49:08.149 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1223 - 🔍 正常推送: 任务12, 帧数6, 检测到4个目标，无告警
2025-07-28 16:49:08.264 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:08.264 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:08.264 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:08.264 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:08.266 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3704 - 任务 12 队列满，替换旧帧
2025-07-28 16:49:08.280 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:08.281 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 4
2025-07-28 16:49:08.281 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 321, 251]}
2025-07-28 16:49:08.281 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 321, 251]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 16:49:08.281 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:08.281 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:08.281 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:08.281 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:08.282 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:08.282 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:08.282 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:08.282 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:08.282 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:08.282 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:08.283 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:08.283 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:08.283 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:08.283 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:08.286 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:08.286 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:08.287 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:08.287 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 4 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:08.287 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:08.287 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4], 'unique_vehicle_ids': [1, 2, 3, 4], 'vehicle_count': 4, 'unique_vehicle_count': 4}
2025-07-28 16:49:08.288 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4]
2025-07-28 16:49:08.288 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4]
2025-07-28 16:49:08.288 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 4
2025-07-28 16:49:08.288 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 189, 321, 251]
2025-07-28 16:49:08.288 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 149, 328, 186]
2025-07-28 16:49:08.288 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 134, 355, 162]
2025-07-28 16:49:08.288 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 4 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 321, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.34, 'name': 'car', 'ch_name': 'car', 'xyxy': [512, 68, 535, 91], 'track_id': 4, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:08.286740', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4], 'unique_vehicle_ids': [1, 2, 3, 4], 'vehicle_count': 4, 'unique_vehicle_count': 4}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692548.286748}
2025-07-28 16:49:08.403 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:08.403 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:08.403 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:08.403 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:08.405 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3704 - 任务 12 队列满，替换旧帧
2025-07-28 16:49:08.417 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:08.417 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 16:49:08.417 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 16:49:08.418 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [289, 149, 328, 188]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 356, 162]}]
2025-07-28 16:49:08.418 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:08.418 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:08.418 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:08.418 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:08.418 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:08.419 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:08.419 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:08.419 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:08.419 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:08.419 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:08.420 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:08.420 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:08.420 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:08.420 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:08.423 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:08.423 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:08.423 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:08.423 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 4 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:08.423 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:08.423 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4], 'unique_vehicle_ids': [1, 2, 3, 4], 'vehicle_count': 4, 'unique_vehicle_count': 4}
2025-07-28 16:49:08.423 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4]
2025-07-28 16:49:08.424 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4]
2025-07-28 16:49:08.424 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 4
2025-07-28 16:49:08.424 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 190, 321, 251]
2025-07-28 16:49:08.425 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[289, 149, 328, 188]
2025-07-28 16:49:08.425 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 134, 356, 162]
2025-07-28 16:49:08.425 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 4 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [289, 149, 328, 188], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 356, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.34, 'name': 'car', 'ch_name': 'car', 'xyxy': [512, 68, 535, 91], 'track_id': 4, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:08.423230', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4], 'unique_vehicle_ids': [1, 2, 3, 4], 'vehicle_count': 4, 'unique_vehicle_count': 4}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692548.423237}
2025-07-28 16:49:08.530 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:08.530 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:08.530 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:08.530 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:08.532 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3704 - 任务 12 队列满，替换旧帧
2025-07-28 16:49:08.634 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:08.634 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 16:49:08.634 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 16:49:08.635 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 356, 162]}]
2025-07-28 16:49:08.635 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:08.635 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:08.636 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:08.636 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:08.636 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:08.636 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:08.636 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:08.637 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:08.637 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:08.637 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:08.637 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:08.638 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:08.638 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:08.638 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:08.641 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:08.641 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:08.641 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:08.641 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 4 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:08.641 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:08.641 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4], 'unique_vehicle_ids': [1, 2, 3, 4], 'vehicle_count': 4, 'unique_vehicle_count': 4}
2025-07-28 16:49:08.641 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4]
2025-07-28 16:49:08.642 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4]
2025-07-28 16:49:08.642 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 4
2025-07-28 16:49:08.642 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 190, 321, 251]
2025-07-28 16:49:08.642 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 149, 328, 186]
2025-07-28 16:49:08.643 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 134, 356, 162]
2025-07-28 16:49:08.643 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 4 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 356, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.34, 'name': 'car', 'ch_name': 'car', 'xyxy': [512, 68, 535, 91], 'track_id': 4, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:08.641278', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4], 'unique_vehicle_ids': [1, 2, 3, 4], 'vehicle_count': 4, 'unique_vehicle_count': 4}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692548.641286}
2025-07-28 16:49:08.644 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=True, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:08.644 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:08.644 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 3, 配置区域数量: 0
2025-07-28 16:49:08.644 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:08.645 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:08.645 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:08.645 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3811 - 绘制完成: 绘制3个检测框, 跳过0个
2025-07-28 16:49:08.646 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3704 - 任务 12 队列满，替换旧帧
2025-07-28 16:49:08.647 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1223 - 🔍 正常推送: 任务12, 帧数12, 检测到3个目标，无告警
2025-07-28 16:49:08.750 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:08.750 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:08.750 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:08.750 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:08.752 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3704 - 任务 12 队列满，替换旧帧
2025-07-28 16:49:08.768 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:08.768 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 16:49:08.768 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 16:49:08.769 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 356, 162]}]
2025-07-28 16:49:08.769 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:08.769 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:08.769 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:08.769 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:08.769 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:08.769 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:08.770 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:08.770 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:08.770 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:08.771 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:08.771 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:08.771 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:08.771 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:08.771 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:08.774 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:08.774 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:08.774 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:08.774 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 4 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:08.774 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:08.774 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4], 'unique_vehicle_ids': [1, 2, 3, 4], 'vehicle_count': 4, 'unique_vehicle_count': 4}
2025-07-28 16:49:08.775 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4]
2025-07-28 16:49:08.775 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4]
2025-07-28 16:49:08.775 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 4
2025-07-28 16:49:08.775 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 190, 321, 251]
2025-07-28 16:49:08.775 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 149, 328, 186]
2025-07-28 16:49:08.776 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 134, 356, 162]
2025-07-28 16:49:08.776 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 4 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 356, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.34, 'name': 'car', 'ch_name': 'car', 'xyxy': [512, 68, 535, 91], 'track_id': 4, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:08.774360', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4], 'unique_vehicle_ids': [1, 2, 3, 4], 'vehicle_count': 4, 'unique_vehicle_count': 4}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692548.7743666}
2025-07-28 16:49:08.891 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:08.892 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:08.892 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:08.892 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:08.893 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3704 - 任务 12 队列满，替换旧帧
2025-07-28 16:49:08.910 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:08.910 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 16:49:08.910 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 16:49:08.910 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 16:49:08.911 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:08.911 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:08.911 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:08.911 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:08.911 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:08.911 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:08.912 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:08.912 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:08.912 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:08.912 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:08.912 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:08.913 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:08.913 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:08.913 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:08.916 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:08.916 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:08.917 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:08.917 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 4 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:08.917 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:08.917 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4], 'unique_vehicle_ids': [1, 2, 3, 4], 'vehicle_count': 4, 'unique_vehicle_count': 4}
2025-07-28 16:49:08.917 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4]
2025-07-28 16:49:08.917 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4]
2025-07-28 16:49:08.917 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 4
2025-07-28 16:49:08.918 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 190, 321, 251]
2025-07-28 16:49:08.918 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 149, 328, 186]
2025-07-28 16:49:08.918 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 134, 355, 162]
2025-07-28 16:49:08.919 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 4 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.34, 'name': 'car', 'ch_name': 'car', 'xyxy': [512, 68, 535, 91], 'track_id': 4, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:08.916762', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4], 'unique_vehicle_ids': [1, 2, 3, 4], 'vehicle_count': 4, 'unique_vehicle_count': 4}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692548.91677}
2025-07-28 16:49:09.033 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:09.033 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:09.033 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:09.033 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:09.035 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3704 - 任务 12 队列满，替换旧帧
2025-07-28 16:49:09.134 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:09.134 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 4
2025-07-28 16:49:09.134 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 16:49:09.134 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.54, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 187]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 16:49:09.134 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:09.135 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:09.135 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:09.135 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:09.135 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:09.135 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:09.136 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:09.136 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:09.136 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:09.136 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:09.137 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:09.137 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:09.137 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:09.137 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:09.140 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:09.140 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:09.140 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:09.140 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 4 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:09.141 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:09.141 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4], 'unique_vehicle_ids': [1, 2, 3, 4], 'vehicle_count': 4, 'unique_vehicle_count': 4}
2025-07-28 16:49:09.141 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4]
2025-07-28 16:49:09.141 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4]
2025-07-28 16:49:09.141 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 4
2025-07-28 16:49:09.141 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 190, 321, 251]
2025-07-28 16:49:09.142 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 149, 328, 187]
2025-07-28 16:49:09.142 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 134, 355, 162]
2025-07-28 16:49:09.142 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 4 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.54, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 187], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.27, 'name': 'car', 'ch_name': 'car', 'xyxy': [512, 66, 533, 86], 'track_id': 4, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:09.140615', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4], 'unique_vehicle_ids': [1, 2, 3, 4], 'vehicle_count': 4, 'unique_vehicle_count': 4}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692549.1406226}
2025-07-28 16:49:09.143 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=True, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:09.143 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:09.143 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 4, 配置区域数量: 0
2025-07-28 16:49:09.143 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:09.144 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:09.144 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:09.144 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:09.144 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3811 - 绘制完成: 绘制4个检测框, 跳过0个
2025-07-28 16:49:09.146 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3704 - 任务 12 队列满，替换旧帧
2025-07-28 16:49:09.146 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1223 - 🔍 正常推送: 任务12, 帧数18, 检测到4个目标，无告警
2025-07-28 16:49:09.254 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:09.255 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:09.255 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:09.255 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:09.257 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3704 - 任务 12 队列满，替换旧帧
2025-07-28 16:49:09.270 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:09.270 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 4
2025-07-28 16:49:09.270 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 16:49:09.270 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 16:49:09.270 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:09.270 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:09.270 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:09.271 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:09.271 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:09.271 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:09.271 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:09.272 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:09.272 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:09.272 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:09.272 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:09.273 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:09.273 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:09.273 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:09.275 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:09.275 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:09.276 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:09.276 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 4 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:09.276 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:09.276 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4], 'unique_vehicle_ids': [1, 2, 3, 4], 'vehicle_count': 4, 'unique_vehicle_count': 4}
2025-07-28 16:49:09.276 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4]
2025-07-28 16:49:09.276 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4]
2025-07-28 16:49:09.276 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 4
2025-07-28 16:49:09.276 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 190, 321, 251]
2025-07-28 16:49:09.277 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 149, 328, 186]
2025-07-28 16:49:09.277 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 134, 355, 162]
2025-07-28 16:49:09.277 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 4 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.29, 'name': 'car', 'ch_name': 'car', 'xyxy': [511, 67, 532, 86], 'track_id': 4, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:09.275799', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4], 'unique_vehicle_ids': [1, 2, 3, 4], 'vehicle_count': 4, 'unique_vehicle_count': 4}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692549.2758062}
2025-07-28 16:49:09.382 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:09.382 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:09.382 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:09.383 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:09.384 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3704 - 任务 12 队列满，替换旧帧
2025-07-28 16:49:09.401 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:09.401 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 4
2025-07-28 16:49:09.401 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 16:49:09.401 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 16:49:09.401 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:09.401 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:09.401 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:09.402 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:09.402 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:09.402 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:09.402 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:09.403 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:09.403 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:09.403 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:09.403 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:09.403 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:09.404 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:09.404 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:09.407 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:09.407 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:09.407 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:09.407 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 4 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:09.407 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:09.407 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4], 'unique_vehicle_ids': [1, 2, 3, 4], 'vehicle_count': 4, 'unique_vehicle_count': 4}
2025-07-28 16:49:09.408 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4]
2025-07-28 16:49:09.408 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4]
2025-07-28 16:49:09.408 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 4
2025-07-28 16:49:09.408 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 190, 321, 251]
2025-07-28 16:49:09.409 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 149, 328, 186]
2025-07-28 16:49:09.409 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 134, 355, 162]
2025-07-28 16:49:09.409 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 4 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [510, 67, 532, 86], 'track_id': 4, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:09.407325', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4], 'unique_vehicle_ids': [1, 2, 3, 4], 'vehicle_count': 4, 'unique_vehicle_count': 4}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692549.4073334}
2025-07-28 16:49:09.523 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:09.524 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:09.524 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:09.524 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:09.525 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3704 - 任务 12 队列满，替换旧帧
2025-07-28 16:49:09.637 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:09.637 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 4
2025-07-28 16:49:09.637 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 16:49:09.637 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 16:49:09.637 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:09.637 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:09.637 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:09.638 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:09.638 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:09.638 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:09.638 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:09.639 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:09.639 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:09.639 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:09.639 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:09.640 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:09.640 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:09.640 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:09.643 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:09.643 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:09.643 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:09.643 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 4 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:09.644 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:09.644 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4], 'unique_vehicle_ids': [1, 2, 3, 4], 'vehicle_count': 4, 'unique_vehicle_count': 4}
2025-07-28 16:49:09.644 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4]
2025-07-28 16:49:09.644 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4]
2025-07-28 16:49:09.644 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 4
2025-07-28 16:49:09.644 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 190, 321, 251]
2025-07-28 16:49:09.645 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 149, 328, 186]
2025-07-28 16:49:09.645 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 134, 355, 162]
2025-07-28 16:49:09.645 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 4 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.3, 'name': 'car', 'ch_name': 'car', 'xyxy': [510, 67, 532, 86], 'track_id': 4, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:09.643509', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4], 'unique_vehicle_ids': [1, 2, 3, 4], 'vehicle_count': 4, 'unique_vehicle_count': 4}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692549.6435168}
2025-07-28 16:49:09.646 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=True, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:09.646 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:09.646 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 4, 配置区域数量: 0
2025-07-28 16:49:09.647 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:09.647 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:09.647 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:09.647 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:09.647 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3811 - 绘制完成: 绘制4个检测框, 跳过0个
2025-07-28 16:49:09.649 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3704 - 任务 12 队列满，替换旧帧
2025-07-28 16:49:09.649 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1223 - 🔍 正常推送: 任务12, 帧数24, 检测到4个目标，无告警
2025-07-28 16:49:09.758 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:09.758 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:09.758 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:09.758 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:09.760 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3704 - 任务 12 队列满，替换旧帧
2025-07-28 16:49:09.776 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:09.776 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 4
2025-07-28 16:49:09.776 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 16:49:09.777 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 188]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 356, 162]}]
2025-07-28 16:49:09.777 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:09.777 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:09.777 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:09.777 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:09.777 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:09.778 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:09.778 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:09.778 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:09.778 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:09.779 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:09.779 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:09.779 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:09.779 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:09.780 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:09.785 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:09.785 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:09.786 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:09.786 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 4 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:09.786 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:09.786 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4], 'unique_vehicle_ids': [1, 2, 3, 4], 'vehicle_count': 4, 'unique_vehicle_count': 4}
2025-07-28 16:49:09.786 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4]
2025-07-28 16:49:09.787 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4]
2025-07-28 16:49:09.787 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 4
2025-07-28 16:49:09.787 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 190, 321, 251]
2025-07-28 16:49:09.788 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 149, 328, 188]
2025-07-28 16:49:09.788 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 134, 356, 162]
2025-07-28 16:49:09.788 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 4 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 188], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 356, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.31, 'name': 'car', 'ch_name': 'car', 'xyxy': [509, 65, 532, 84], 'track_id': 4, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:09.785531', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4], 'unique_vehicle_ids': [1, 2, 3, 4], 'vehicle_count': 4, 'unique_vehicle_count': 4}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692549.7855403}
2025-07-28 16:49:09.798 | c55fd45bc57e41538b78dd31e319f5a2 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 16:49:09.811 | 8a8c82d93869450b9c167ac74aa9bad3 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 16:49:09.897 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:09.897 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:09.897 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:09.897 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:09.899 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3704 - 任务 12 队列满，替换旧帧
2025-07-28 16:49:09.919 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:09.921 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 5
2025-07-28 16:49:09.922 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 16:49:09.922 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 188]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [1229, 190, 1280, 214]}]
2025-07-28 16:49:09.922 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:09.923 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:09.923 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:09.924 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:09.924 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:09.924 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:09.924 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:09.925 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:09.925 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:09.925 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:09.925 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:09.926 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:09.926 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:09.926 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:09.930 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:09.930 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:09.930 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:09.931 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 5 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:09.931 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:09.931 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4, 5], 'unique_vehicle_ids': [1, 2, 3, 4, 5], 'vehicle_count': 5, 'unique_vehicle_count': 5}
2025-07-28 16:49:09.931 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4, 5]
2025-07-28 16:49:09.931 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4, 5]
2025-07-28 16:49:09.931 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 5
2025-07-28 16:49:09.931 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 190, 321, 251]
2025-07-28 16:49:09.932 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 149, 328, 188]
2025-07-28 16:49:09.932 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 133, 355, 162]
2025-07-28 16:49:09.932 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 5 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 188], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 133, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.29, 'name': 'car', 'ch_name': 'car', 'xyxy': [509, 65, 531, 84], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [1229, 190, 1280, 214], 'track_id': 5, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:09.930575', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4, 5], 'unique_vehicle_ids': [1, 2, 3, 4, 5], 'vehicle_count': 5, 'unique_vehicle_count': 5}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692549.9305828}
2025-07-28 16:49:09.942 | d8ef3ca3486248d0a6ffa4dda7c2a974 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 16:49:09.942 | d8ef3ca3486248d0a6ffa4dda7c2a974 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 16:49:09.942 | d8ef3ca3486248d0a6ffa4dda7c2a974 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 16:49:09.942 | d8ef3ca3486248d0a6ffa4dda7c2a974 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 16:49:09.943 | d8ef3ca3486248d0a6ffa4dda7c2a974 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 16:49:09.943 | d8ef3ca3486248d0a6ffa4dda7c2a974 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 16:49:09.943 | d8ef3ca3486248d0a6ffa4dda7c2a974 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 16:49:09.943 | d8ef3ca3486248d0a6ffa4dda7c2a974 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 16:49:09.943 | d8ef3ca3486248d0a6ffa4dda7c2a974 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 16:49:09.946 | d8ef3ca3486248d0a6ffa4dda7c2a974 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-28 16:49:09.946 | d8ef3ca3486248d0a6ffa4dda7c2a974 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 16:49:09.946 | d8ef3ca3486248d0a6ffa4dda7c2a974 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-28 16:49:09.946 | d8ef3ca3486248d0a6ffa4dda7c2a974 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-28 16:49:09.948 | d8ef3ca3486248d0a6ffa4dda7c2a974 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 16:49:09.948 | d8ef3ca3486248d0a6ffa4dda7c2a974 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-28 16:49:09.948 | d8ef3ca3486248d0a6ffa4dda7c2a974 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 16:49:09.948 | d8ef3ca3486248d0a6ffa4dda7c2a974 | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-28 16:49:09.954 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:52 - 开始WebSocket连接: 任务12 (无认证模式)
2025-07-28 16:49:09.954 |  | INFO     | module_stream.service.task_execution_service:add_monitor_client:3941 - 客户端 842372e5-d7fc-40f9-9e09-5e1a932bf4bb 已连接到任务 12 的监控流
2025-07-28 16:49:09.955 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:73 - 客户端 842372e5-d7fc-40f9-9e09-5e1a932bf4bb 连接到任务 12 的监控流 (无认证模式)
2025-07-28 16:49:10.037 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:10.038 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:10.038 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:10.038 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:10.137 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:10.137 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 6
2025-07-28 16:49:10.137 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}
2025-07-28 16:49:10.137 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.5, 'name': 'car', 'ch_name': 'car', 'xyxy': [1215, 187, 1278, 213]}]
2025-07-28 16:49:10.137 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:10.137 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:10.137 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:10.138 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:10.138 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:10.138 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:10.138 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:10.139 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:10.139 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:10.139 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:10.139 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:10.140 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:10.140 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:10.140 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:10.143 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:10.144 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:10.144 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:10.144 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 7 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:10.144 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:10.145 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7], 'vehicle_count': 7, 'unique_vehicle_count': 7}
2025-07-28 16:49:10.145 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4, 5, 6, 7]
2025-07-28 16:49:10.145 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4, 5, 6, 7]
2025-07-28 16:49:10.145 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 7
2025-07-28 16:49:10.146 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 190, 320, 251]
2025-07-28 16:49:10.146 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 149, 328, 186]
2025-07-28 16:49:10.146 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 133, 355, 162]
2025-07-28 16:49:10.146 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 7 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 133, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.29, 'name': 'car', 'ch_name': 'car', 'xyxy': [509, 65, 531, 84], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.5, 'name': 'car', 'ch_name': 'car', 'xyxy': [1215, 187, 1278, 213], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [1163, 201, 1270, 252], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.25, 'name': 'car', 'ch_name': 'car', 'xyxy': [1222, 194, 1279, 228], 'track_id': 7, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:10.143921', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7], 'vehicle_count': 7, 'unique_vehicle_count': 7}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692550.1439285}
2025-07-28 16:49:10.147 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=True, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:10.148 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:10.148 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 6, 配置区域数量: 0
2025-07-28 16:49:10.148 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:10.148 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:10.148 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:10.148 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:10.148 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:10.148 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:10.148 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3811 - 绘制完成: 绘制6个检测框, 跳过0个
2025-07-28 16:49:10.150 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1223 - 🔍 正常推送: 任务12, 帧数30, 检测到6个目标，无告警
2025-07-28 16:49:10.258 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:10.258 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:10.258 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:10.258 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:10.273 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:10.273 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 16:49:10.273 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}
2025-07-28 16:49:10.273 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}, {'label': 2, 'conf': 0.54, 'name': 'car', 'ch_name': 'car', 'xyxy': [289, 149, 328, 187]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 133, 355, 162]}]
2025-07-28 16:49:10.273 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:10.273 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:10.274 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:10.274 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:10.274 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:10.274 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:10.274 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:10.275 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:10.275 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:10.275 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:10.275 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:10.275 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:10.276 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:10.276 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:10.280 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:10.280 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:10.280 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:10.280 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 7 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:10.280 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:10.280 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7], 'vehicle_count': 7, 'unique_vehicle_count': 7}
2025-07-28 16:49:10.280 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4, 5, 6, 7]
2025-07-28 16:49:10.280 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4, 5, 6, 7]
2025-07-28 16:49:10.280 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 7
2025-07-28 16:49:10.281 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 190, 320, 251]
2025-07-28 16:49:10.281 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[289, 149, 328, 187]
2025-07-28 16:49:10.281 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 133, 355, 162]
2025-07-28 16:49:10.281 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 7 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.54, 'name': 'car', 'ch_name': 'car', 'xyxy': [289, 149, 328, 187], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 133, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.29, 'name': 'car', 'ch_name': 'car', 'xyxy': [509, 65, 531, 84], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.5, 'name': 'car', 'ch_name': 'car', 'xyxy': [1215, 187, 1278, 213], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [1163, 201, 1270, 252], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.25, 'name': 'car', 'ch_name': 'car', 'xyxy': [1222, 194, 1279, 228], 'track_id': 7, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:10.279984', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7], 'vehicle_count': 7, 'unique_vehicle_count': 7}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692550.2799935}
2025-07-28 16:49:10.449 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:10.449 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:10.449 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:10.449 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:10.465 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:10.466 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 7
2025-07-28 16:49:10.466 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.7, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}
2025-07-28 16:49:10.466 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.7, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 16:49:10.466 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:10.467 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:10.467 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:10.467 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:10.467 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:10.467 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:10.467 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:10.468 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:10.468 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:10.468 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:10.468 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:10.469 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:10.469 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:10.469 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:10.472 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:10.472 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:10.472 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:10.473 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 8 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:10.473 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:10.473 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8], 'vehicle_count': 8, 'unique_vehicle_count': 8}
2025-07-28 16:49:10.473 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4, 5, 6, 7, 8]
2025-07-28 16:49:10.473 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4, 5, 6, 7, 8]
2025-07-28 16:49:10.473 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 8
2025-07-28 16:49:10.473 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 190, 320, 251]
2025-07-28 16:49:10.474 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 149, 328, 186]
2025-07-28 16:49:10.474 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 134, 355, 162]
2025-07-28 16:49:10.474 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 8 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.7, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.29, 'name': 'car', 'ch_name': 'car', 'xyxy': [509, 65, 531, 84], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.25, 'name': 'car', 'ch_name': 'car', 'xyxy': [1226, 202, 1280, 232], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.31, 'name': 'car', 'ch_name': 'car', 'xyxy': [1151, 194, 1270, 249], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.35, 'name': 'car', 'ch_name': 'car', 'xyxy': [1170, 182, 1241, 206], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.25, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 193, 1184, 213], 'track_id': 8, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:10.472670', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8], 'vehicle_count': 8, 'unique_vehicle_count': 8}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692550.4726784}
2025-07-28 16:49:10.652 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:10.652 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:10.652 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:10.652 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:10.670 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:10.670 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 6
2025-07-28 16:49:10.671 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.7, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}
2025-07-28 16:49:10.671 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.7, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}, {'label': 2, 'conf': 0.6, 'name': 'car', 'ch_name': 'car', 'xyxy': [1147, 181, 1225, 211]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}]
2025-07-28 16:49:10.671 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:10.671 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:10.671 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:10.672 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:10.672 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:10.672 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:10.672 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:10.672 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:10.672 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:10.673 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:10.673 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:10.673 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:10.673 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:10.673 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:10.677 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:10.677 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:10.677 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:10.677 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 9 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:10.677 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:10.677 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9], 'vehicle_count': 9, 'unique_vehicle_count': 9}
2025-07-28 16:49:10.677 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4, 5, 6, 7, 8, 9]
2025-07-28 16:49:10.677 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4, 5, 6, 7, 8, 9]
2025-07-28 16:49:10.678 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 9
2025-07-28 16:49:10.678 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 190, 320, 251]
2025-07-28 16:49:10.678 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 149, 328, 186]
2025-07-28 16:49:10.678 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 134, 355, 162]
2025-07-28 16:49:10.679 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 9 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.7, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.29, 'name': 'car', 'ch_name': 'car', 'xyxy': [509, 65, 531, 84], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [1227, 201, 1280, 232], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.31, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 191, 1262, 247], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.35, 'name': 'car', 'ch_name': 'car', 'xyxy': [1170, 182, 1241, 206], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.25, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 193, 1184, 213], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.6, 'name': 'car', 'ch_name': 'car', 'xyxy': [1147, 181, 1225, 211], 'track_id': 9, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:10.677115', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9], 'vehicle_count': 9, 'unique_vehicle_count': 9}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692550.677122}
2025-07-28 16:49:10.680 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=True, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:10.680 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:10.680 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 6, 配置区域数量: 0
2025-07-28 16:49:10.680 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:10.680 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:10.680 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:10.680 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:10.681 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:10.681 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:10.681 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3811 - 绘制完成: 绘制6个检测框, 跳过0个
2025-07-28 16:49:10.682 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1223 - 🔍 正常推送: 任务12, 帧数36, 检测到6个目标，无告警
2025-07-28 16:49:10.885 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:10.885 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:10.885 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:10.885 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:10.901 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:10.901 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 6
2025-07-28 16:49:10.902 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}
2025-07-28 16:49:10.902 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251]}, {'label': 2, 'conf': 0.62, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 183, 1206, 210]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [289, 149, 328, 187]}]
2025-07-28 16:49:10.902 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:10.902 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:10.903 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:10.903 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:10.903 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:10.903 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:10.903 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:10.904 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:10.904 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:10.904 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:10.904 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:10.905 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:10.905 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:10.905 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:10.909 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:10.909 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:10.909 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:10.909 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 10 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:10.909 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:10.909 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], 'vehicle_count': 10, 'unique_vehicle_count': 10}
2025-07-28 16:49:10.909 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
2025-07-28 16:49:10.910 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
2025-07-28 16:49:10.910 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 10
2025-07-28 16:49:10.910 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 190, 320, 251]
2025-07-28 16:49:10.911 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[289, 149, 328, 187]
2025-07-28 16:49:10.911 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 134, 355, 162]
2025-07-28 16:49:10.911 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 10 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 320, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [289, 149, 328, 187], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.29, 'name': 'car', 'ch_name': 'car', 'xyxy': [509, 65, 531, 84], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.29, 'name': 'car', 'ch_name': 'car', 'xyxy': [1229, 202, 1280, 232], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [1149, 203, 1275, 256], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.35, 'name': 'car', 'ch_name': 'car', 'xyxy': [1170, 182, 1241, 206], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.25, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 193, 1184, 213], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.6, 'name': 'car', 'ch_name': 'car', 'xyxy': [1147, 181, 1225, 211], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.62, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 183, 1206, 210], 'track_id': 10, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:10.909124', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], 'vehicle_count': 10, 'unique_vehicle_count': 10}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692550.909133}
2025-07-28 16:49:11.088 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:11.088 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:11.088 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:11.088 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:11.102 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:11.102 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 6
2025-07-28 16:49:11.102 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 190, 321, 251]}
2025-07-28 16:49:11.102 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 190, 321, 251]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [1142, 183, 1190, 212]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [289, 149, 328, 187]}]
2025-07-28 16:49:11.103 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:11.103 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:11.103 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:11.103 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:11.103 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:11.103 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:11.103 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:11.104 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:11.104 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:11.104 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:11.104 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:11.104 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:11.105 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:11.105 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:11.109 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:11.109 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:11.109 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:11.109 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 11 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:11.110 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:11.110 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}
2025-07-28 16:49:11.110 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:11.110 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:11.110 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 11
2025-07-28 16:49:11.110 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[263, 190, 321, 251]
2025-07-28 16:49:11.111 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[289, 149, 328, 187]
2025-07-28 16:49:11.111 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 134, 355, 162]
2025-07-28 16:49:11.111 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 11 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 190, 321, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [289, 149, 328, 187], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.29, 'name': 'car', 'ch_name': 'car', 'xyxy': [509, 65, 531, 84], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.3, 'name': 'car', 'ch_name': 'car', 'xyxy': [1228, 203, 1279, 232], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [1159, 205, 1270, 253], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.35, 'name': 'car', 'ch_name': 'car', 'xyxy': [1170, 182, 1241, 206], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.25, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 193, 1184, 213], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.6, 'name': 'car', 'ch_name': 'car', 'xyxy': [1147, 181, 1225, 211], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.62, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 183, 1206, 210], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [1142, 183, 1190, 212], 'track_id': 11, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:11.109367', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692551.1093774}
2025-07-28 16:49:11.294 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:11.294 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:11.294 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:11.294 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:11.313 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:11.314 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 16:49:11.314 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 16:49:11.314 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [289, 149, 328, 187]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 133, 355, 162]}]
2025-07-28 16:49:11.314 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:11.315 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:11.315 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:11.315 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:11.315 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:11.315 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:11.315 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:11.316 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:11.316 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:11.316 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:11.316 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:11.316 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:11.317 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:11.317 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:11.320 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:11.320 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:11.320 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:11.320 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 11 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:11.320 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:11.320 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}
2025-07-28 16:49:11.320 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:11.321 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:11.321 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 11
2025-07-28 16:49:11.321 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 190, 321, 251]
2025-07-28 16:49:11.321 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[289, 149, 328, 187]
2025-07-28 16:49:11.322 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 133, 355, 162]
2025-07-28 16:49:11.322 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 11 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [289, 149, 328, 187], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 133, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.29, 'name': 'car', 'ch_name': 'car', 'xyxy': [509, 65, 531, 84], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.3, 'name': 'car', 'ch_name': 'car', 'xyxy': [1228, 203, 1279, 232], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [1159, 205, 1270, 253], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.35, 'name': 'car', 'ch_name': 'car', 'xyxy': [1170, 182, 1241, 206], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.25, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 193, 1184, 213], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.6, 'name': 'car', 'ch_name': 'car', 'xyxy': [1147, 181, 1225, 211], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.62, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 183, 1206, 210], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [1142, 183, 1190, 212], 'track_id': 11, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:11.320327', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692551.3203351}
2025-07-28 16:49:11.323 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=True, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:11.323 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:11.324 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 3, 配置区域数量: 0
2025-07-28 16:49:11.324 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:11.324 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:11.324 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:11.324 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3811 - 绘制完成: 绘制3个检测框, 跳过0个
2025-07-28 16:49:11.326 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1223 - 🔍 正常推送: 任务12, 帧数42, 检测到3个目标，无告警
2025-07-28 16:49:11.529 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:11.529 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:11.529 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:11.529 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:11.546 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:11.546 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 16:49:11.547 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}
2025-07-28 16:49:11.547 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 133, 355, 162]}]
2025-07-28 16:49:11.547 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:11.547 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:11.547 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:11.547 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:11.547 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:11.547 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:11.548 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:11.548 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:11.548 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:11.548 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:11.549 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:11.549 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:11.549 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:11.549 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:11.553 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:11.553 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:11.553 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:11.553 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 11 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:11.553 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:11.554 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}
2025-07-28 16:49:11.554 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:11.554 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:11.554 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 11
2025-07-28 16:49:11.554 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 190, 321, 251]
2025-07-28 16:49:11.555 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 149, 328, 186]
2025-07-28 16:49:11.555 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 133, 355, 162]
2025-07-28 16:49:11.555 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 11 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 190, 321, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 133, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.29, 'name': 'car', 'ch_name': 'car', 'xyxy': [509, 65, 531, 84], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.3, 'name': 'car', 'ch_name': 'car', 'xyxy': [1228, 203, 1279, 232], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [1159, 205, 1270, 253], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.35, 'name': 'car', 'ch_name': 'car', 'xyxy': [1170, 182, 1241, 206], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.25, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 193, 1184, 213], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.6, 'name': 'car', 'ch_name': 'car', 'xyxy': [1147, 181, 1225, 211], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.62, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 183, 1206, 210], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [1142, 183, 1190, 212], 'track_id': 11, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:11.553425', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692551.5534341}
2025-07-28 16:49:11.731 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:11.732 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:11.732 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:11.732 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:11.749 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:11.750 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 16:49:11.750 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 190, 321, 251]}
2025-07-28 16:49:11.750 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 190, 321, 251]}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 133, 355, 162]}]
2025-07-28 16:49:11.750 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:11.750 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:11.751 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:11.751 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:11.751 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:11.751 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:11.751 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:11.752 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:11.752 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:11.752 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:11.752 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:11.753 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:11.753 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:11.753 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:11.756 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:11.756 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:11.756 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:11.756 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 11 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:11.756 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:11.757 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}
2025-07-28 16:49:11.757 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:11.757 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:11.757 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 11
2025-07-28 16:49:11.758 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[263, 190, 321, 251]
2025-07-28 16:49:11.758 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 149, 328, 186]
2025-07-28 16:49:11.758 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 133, 355, 162]
2025-07-28 16:49:11.759 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 11 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 190, 321, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 133, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.29, 'name': 'car', 'ch_name': 'car', 'xyxy': [509, 65, 531, 84], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.3, 'name': 'car', 'ch_name': 'car', 'xyxy': [1228, 203, 1279, 232], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [1159, 205, 1270, 253], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.35, 'name': 'car', 'ch_name': 'car', 'xyxy': [1170, 182, 1241, 206], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.25, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 193, 1184, 213], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.6, 'name': 'car', 'ch_name': 'car', 'xyxy': [1147, 181, 1225, 211], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.62, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 183, 1206, 210], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [1142, 183, 1190, 212], 'track_id': 11, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:11.756616', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692551.7566228}
2025-07-28 16:49:11.935 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:11.936 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:11.936 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:11.936 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:11.949 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:11.950 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 16:49:11.950 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 190, 321, 251]}
2025-07-28 16:49:11.950 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 190, 321, 251]}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 133, 355, 162]}]
2025-07-28 16:49:11.950 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:11.950 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:11.951 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:11.951 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:11.951 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:11.951 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:11.951 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:11.952 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:11.952 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:11.952 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:11.952 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:11.953 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:11.953 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:11.953 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:11.956 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:11.956 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:11.957 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:11.957 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 11 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:11.957 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:11.957 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}
2025-07-28 16:49:11.957 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:11.958 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:11.958 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 11
2025-07-28 16:49:11.958 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[263, 190, 321, 251]
2025-07-28 16:49:11.958 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 149, 328, 186]
2025-07-28 16:49:11.959 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 133, 355, 162]
2025-07-28 16:49:11.959 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 11 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 190, 321, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 133, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.29, 'name': 'car', 'ch_name': 'car', 'xyxy': [509, 65, 531, 84], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.3, 'name': 'car', 'ch_name': 'car', 'xyxy': [1228, 203, 1279, 232], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [1159, 205, 1270, 253], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.35, 'name': 'car', 'ch_name': 'car', 'xyxy': [1170, 182, 1241, 206], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.25, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 193, 1184, 213], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.6, 'name': 'car', 'ch_name': 'car', 'xyxy': [1147, 181, 1225, 211], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.62, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 183, 1206, 210], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [1142, 183, 1190, 212], 'track_id': 11, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:11.956678', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692551.9566865}
2025-07-28 16:49:11.960 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=True, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:11.960 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:11.960 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 3, 配置区域数量: 0
2025-07-28 16:49:11.961 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:11.961 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:11.961 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:11.961 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3811 - 绘制完成: 绘制3个检测框, 跳过0个
2025-07-28 16:49:11.963 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1223 - 🔍 正常推送: 任务12, 帧数48, 检测到3个目标，无告警
2025-07-28 16:49:12.075 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:12.075 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:12.076 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:12.076 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:12.091 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:12.091 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 16:49:12.092 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 190, 321, 251]}
2025-07-28 16:49:12.092 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 190, 321, 251]}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 187]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 16:49:12.092 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:12.092 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:12.092 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:12.092 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:12.092 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:12.092 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:12.093 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:12.093 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:12.093 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:12.093 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:12.093 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:12.094 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:12.094 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:12.094 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:12.098 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:12.098 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:12.098 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:12.098 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 11 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:12.098 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:12.098 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}
2025-07-28 16:49:12.098 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:12.099 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:12.099 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 11
2025-07-28 16:49:12.099 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[263, 190, 321, 251]
2025-07-28 16:49:12.099 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 149, 328, 187]
2025-07-28 16:49:12.100 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 134, 355, 162]
2025-07-28 16:49:12.100 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 11 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 190, 321, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 187], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.29, 'name': 'car', 'ch_name': 'car', 'xyxy': [509, 65, 531, 84], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.3, 'name': 'car', 'ch_name': 'car', 'xyxy': [1228, 203, 1279, 232], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [1159, 205, 1270, 253], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.35, 'name': 'car', 'ch_name': 'car', 'xyxy': [1170, 182, 1241, 206], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.25, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 193, 1184, 213], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.6, 'name': 'car', 'ch_name': 'car', 'xyxy': [1147, 181, 1225, 211], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.62, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 183, 1206, 210], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [1142, 183, 1190, 212], 'track_id': 11, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:12.098340', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692552.0983493}
2025-07-28 16:49:12.264 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:12.264 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:12.264 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:12.265 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:12.281 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:12.281 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 16:49:12.281 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 250]}
2025-07-28 16:49:12.281 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 250]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 188]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [319, 133, 355, 162]}]
2025-07-28 16:49:12.281 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:12.281 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:12.281 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:12.282 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:12.282 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:12.282 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:12.282 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:12.282 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:12.283 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:12.283 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:12.283 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:12.283 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:12.284 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:12.284 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:12.287 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:12.287 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:12.288 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:12.288 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 11 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:12.288 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:12.288 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}
2025-07-28 16:49:12.288 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:12.288 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:12.288 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 11
2025-07-28 16:49:12.288 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 189, 320, 250]
2025-07-28 16:49:12.289 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 149, 328, 188]
2025-07-28 16:49:12.289 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[319, 133, 355, 162]
2025-07-28 16:49:12.289 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 11 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 250], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 188], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [319, 133, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.29, 'name': 'car', 'ch_name': 'car', 'xyxy': [509, 65, 531, 84], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.3, 'name': 'car', 'ch_name': 'car', 'xyxy': [1228, 203, 1279, 232], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [1159, 205, 1270, 253], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.35, 'name': 'car', 'ch_name': 'car', 'xyxy': [1170, 182, 1241, 206], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.25, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 193, 1184, 213], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.6, 'name': 'car', 'ch_name': 'car', 'xyxy': [1147, 181, 1225, 211], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.62, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 183, 1206, 210], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [1142, 183, 1190, 212], 'track_id': 11, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:12.287759', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692552.2877667}
2025-07-28 16:49:12.468 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:12.468 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:12.468 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:12.468 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:12.482 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:12.482 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 16:49:12.482 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}
2025-07-28 16:49:12.482 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 188]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 16:49:12.483 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:12.483 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:12.483 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:12.483 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:12.483 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:12.483 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:12.484 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:12.484 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:12.484 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:12.485 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:12.485 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:12.485 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:12.485 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:12.485 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:12.488 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:12.488 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:12.489 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:12.489 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 11 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:12.489 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:12.489 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}
2025-07-28 16:49:12.489 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:12.489 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:12.489 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 11
2025-07-28 16:49:12.490 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 189, 320, 251]
2025-07-28 16:49:12.490 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 149, 328, 188]
2025-07-28 16:49:12.490 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 134, 355, 162]
2025-07-28 16:49:12.490 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 11 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 188], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.29, 'name': 'car', 'ch_name': 'car', 'xyxy': [509, 65, 531, 84], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.3, 'name': 'car', 'ch_name': 'car', 'xyxy': [1228, 203, 1279, 232], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [1159, 205, 1270, 253], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.35, 'name': 'car', 'ch_name': 'car', 'xyxy': [1170, 182, 1241, 206], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.25, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 193, 1184, 213], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.6, 'name': 'car', 'ch_name': 'car', 'xyxy': [1147, 181, 1225, 211], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.62, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 183, 1206, 210], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [1142, 183, 1190, 212], 'track_id': 11, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:12.488786', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692552.488793}
2025-07-28 16:49:12.492 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=True, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:12.492 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:12.492 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 3, 配置区域数量: 0
2025-07-28 16:49:12.492 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:12.492 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:12.492 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:12.492 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3811 - 绘制完成: 绘制3个检测框, 跳过0个
2025-07-28 16:49:12.494 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1223 - 🔍 正常推送: 任务12, 帧数54, 检测到3个目标，无告警
2025-07-28 16:49:12.606 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:12.606 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:12.606 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:12.606 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:12.626 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:12.626 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 16:49:12.626 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}
2025-07-28 16:49:12.626 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 188]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 16:49:12.626 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:12.626 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:12.627 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:12.627 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:12.627 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:12.627 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:12.628 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:12.628 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:12.628 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:12.628 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:12.628 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:12.629 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:12.629 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:12.629 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:12.632 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:12.632 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:12.633 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:12.633 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 11 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:12.633 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:12.633 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}
2025-07-28 16:49:12.633 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:12.633 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:12.633 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 11
2025-07-28 16:49:12.634 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 189, 320, 251]
2025-07-28 16:49:12.634 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 149, 328, 188]
2025-07-28 16:49:12.634 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 134, 355, 162]
2025-07-28 16:49:12.634 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 11 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 188], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.29, 'name': 'car', 'ch_name': 'car', 'xyxy': [509, 65, 531, 84], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.3, 'name': 'car', 'ch_name': 'car', 'xyxy': [1228, 203, 1279, 232], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [1159, 205, 1270, 253], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.35, 'name': 'car', 'ch_name': 'car', 'xyxy': [1170, 182, 1241, 206], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.25, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 193, 1184, 213], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.6, 'name': 'car', 'ch_name': 'car', 'xyxy': [1147, 181, 1225, 211], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.62, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 183, 1206, 210], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [1142, 183, 1190, 212], 'track_id': 11, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:12.632754', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692552.6327624}
2025-07-28 16:49:12.795 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:12.795 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:12.795 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:12.795 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:12.812 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:12.812 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 16:49:12.812 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}
2025-07-28 16:49:12.812 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 187]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 16:49:12.812 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:12.812 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:12.812 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:12.812 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:12.813 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:12.813 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:12.813 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:12.813 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:12.814 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:12.814 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:12.814 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:12.814 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:12.814 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:12.815 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:12.818 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:12.818 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:12.818 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:12.818 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 11 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:12.818 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:12.819 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}
2025-07-28 16:49:12.819 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:12.819 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:12.819 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 11
2025-07-28 16:49:12.819 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 189, 320, 251]
2025-07-28 16:49:12.820 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 149, 328, 187]
2025-07-28 16:49:12.820 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 134, 355, 162]
2025-07-28 16:49:12.820 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 11 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 187], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.29, 'name': 'car', 'ch_name': 'car', 'xyxy': [509, 65, 531, 84], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.3, 'name': 'car', 'ch_name': 'car', 'xyxy': [1228, 203, 1279, 232], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [1159, 205, 1270, 253], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.35, 'name': 'car', 'ch_name': 'car', 'xyxy': [1170, 182, 1241, 206], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.25, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 193, 1184, 213], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.6, 'name': 'car', 'ch_name': 'car', 'xyxy': [1147, 181, 1225, 211], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.62, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 183, 1206, 210], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [1142, 183, 1190, 212], 'track_id': 11, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:12.818550', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692552.8185587}
2025-07-28 16:49:12.996 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:12.997 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:12.997 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:12.997 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:13.014 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:13.015 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 16:49:13.015 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}
2025-07-28 16:49:13.015 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 187]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 16:49:13.015 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:13.015 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:13.015 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:13.015 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:13.016 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:13.016 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:13.016 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:13.016 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:13.030 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:13.032 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:13.032 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:13.033 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:13.034 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:13.034 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:13.042 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:13.043 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:13.043 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:13.043 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 11 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:13.043 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:13.043 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}
2025-07-28 16:49:13.043 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:13.043 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:13.044 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 11
2025-07-28 16:49:13.044 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 189, 320, 251]
2025-07-28 16:49:13.044 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 149, 328, 187]
2025-07-28 16:49:13.044 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 134, 355, 162]
2025-07-28 16:49:13.045 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 11 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 187], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.29, 'name': 'car', 'ch_name': 'car', 'xyxy': [509, 65, 531, 84], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.3, 'name': 'car', 'ch_name': 'car', 'xyxy': [1228, 203, 1279, 232], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [1159, 205, 1270, 253], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.35, 'name': 'car', 'ch_name': 'car', 'xyxy': [1170, 182, 1241, 206], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.25, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 193, 1184, 213], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.6, 'name': 'car', 'ch_name': 'car', 'xyxy': [1147, 181, 1225, 211], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.62, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 183, 1206, 210], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [1142, 183, 1190, 212], 'track_id': 11, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:13.042893', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692553.0429027}
2025-07-28 16:49:13.046 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=True, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:13.046 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:13.046 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 3, 配置区域数量: 0
2025-07-28 16:49:13.046 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:13.046 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:13.046 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:13.046 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3811 - 绘制完成: 绘制3个检测框, 跳过0个
2025-07-28 16:49:13.048 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1223 - 🔍 正常推送: 任务12, 帧数60, 检测到3个目标，无告警
2025-07-28 16:49:13.260 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:13.261 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:13.261 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:13.261 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:13.276 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:13.276 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 16:49:13.276 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}
2025-07-28 16:49:13.277 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}, {'label': 2, 'conf': 0.5, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 187]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 16:49:13.277 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:13.277 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:13.277 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:13.277 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:13.277 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:13.278 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:13.278 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:13.278 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:13.278 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:13.278 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:13.279 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:13.279 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:13.279 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:13.279 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:13.282 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:13.283 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:13.283 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:13.283 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 11 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:13.283 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:13.283 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}
2025-07-28 16:49:13.283 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:13.283 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:13.284 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 11
2025-07-28 16:49:13.284 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 189, 320, 251]
2025-07-28 16:49:13.284 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 149, 328, 187]
2025-07-28 16:49:13.284 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 134, 355, 162]
2025-07-28 16:49:13.285 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 11 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.5, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 187], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.29, 'name': 'car', 'ch_name': 'car', 'xyxy': [509, 65, 531, 84], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.3, 'name': 'car', 'ch_name': 'car', 'xyxy': [1228, 203, 1279, 232], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [1159, 205, 1270, 253], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.35, 'name': 'car', 'ch_name': 'car', 'xyxy': [1170, 182, 1241, 206], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.25, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 193, 1184, 213], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.6, 'name': 'car', 'ch_name': 'car', 'xyxy': [1147, 181, 1225, 211], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.62, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 183, 1206, 210], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [1142, 183, 1190, 212], 'track_id': 11, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:13.282914', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692553.2829206}
2025-07-28 16:49:13.462 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:13.462 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:13.463 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:13.463 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:13.479 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:13.479 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 16:49:13.479 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}
2025-07-28 16:49:13.479 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 188]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 16:49:13.479 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:13.479 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:13.479 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:13.480 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:13.480 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:13.480 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:13.480 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:13.480 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:13.480 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:13.481 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:13.481 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:13.481 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:13.481 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:13.481 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:13.485 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:13.486 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:13.486 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:13.486 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 11 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:13.486 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:13.486 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}
2025-07-28 16:49:13.486 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:13.486 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:13.486 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 11
2025-07-28 16:49:13.487 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 189, 320, 251]
2025-07-28 16:49:13.487 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 149, 328, 188]
2025-07-28 16:49:13.487 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 134, 355, 162]
2025-07-28 16:49:13.487 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 11 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 188], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.29, 'name': 'car', 'ch_name': 'car', 'xyxy': [509, 65, 531, 84], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.3, 'name': 'car', 'ch_name': 'car', 'xyxy': [1228, 203, 1279, 232], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [1159, 205, 1270, 253], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.35, 'name': 'car', 'ch_name': 'car', 'xyxy': [1170, 182, 1241, 206], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.25, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 193, 1184, 213], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.6, 'name': 'car', 'ch_name': 'car', 'xyxy': [1147, 181, 1225, 211], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.62, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 183, 1206, 210], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [1142, 183, 1190, 212], 'track_id': 11, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:13.485944', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692553.485953}
2025-07-28 16:49:13.667 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:13.667 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:13.667 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:13.667 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:13.681 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:13.681 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 16:49:13.681 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}
2025-07-28 16:49:13.681 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}, {'label': 2, 'conf': 0.5, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 187]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 16:49:13.681 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:13.681 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:13.681 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:13.682 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:13.682 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:13.682 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:13.683 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:13.683 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:13.683 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:13.683 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:13.684 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:13.684 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:13.684 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:13.684 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:13.688 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:13.688 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:13.688 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:13.688 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 11 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:13.689 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:13.689 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}
2025-07-28 16:49:13.689 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:13.689 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:13.689 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 11
2025-07-28 16:49:13.689 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 189, 320, 251]
2025-07-28 16:49:13.690 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 149, 328, 187]
2025-07-28 16:49:13.690 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 134, 355, 162]
2025-07-28 16:49:13.691 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 11 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.5, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 187], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.29, 'name': 'car', 'ch_name': 'car', 'xyxy': [509, 65, 531, 84], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.3, 'name': 'car', 'ch_name': 'car', 'xyxy': [1228, 203, 1279, 232], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [1159, 205, 1270, 253], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.35, 'name': 'car', 'ch_name': 'car', 'xyxy': [1170, 182, 1241, 206], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.25, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 193, 1184, 213], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.6, 'name': 'car', 'ch_name': 'car', 'xyxy': [1147, 181, 1225, 211], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.62, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 183, 1206, 210], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [1142, 183, 1190, 212], 'track_id': 11, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:13.688461', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692553.6884687}
2025-07-28 16:49:13.692 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=True, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:13.692 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:13.692 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 3, 配置区域数量: 0
2025-07-28 16:49:13.692 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:13.692 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:13.692 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:13.692 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3811 - 绘制完成: 绘制3个检测框, 跳过0个
2025-07-28 16:49:13.694 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1223 - 🔍 正常推送: 任务12, 帧数66, 检测到3个目标，无告警
2025-07-28 16:49:13.805 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:13.805 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:13.805 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:13.805 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:13.821 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:13.821 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 16:49:13.821 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}
2025-07-28 16:49:13.822 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}, {'label': 2, 'conf': 0.49, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 161]}]
2025-07-28 16:49:13.822 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:13.822 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:13.822 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:13.822 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:13.822 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:13.823 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:13.823 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:13.823 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:13.824 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:13.824 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:13.824 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:13.824 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:13.825 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:13.825 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:13.829 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:13.829 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:13.829 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:13.829 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 11 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:13.829 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:13.829 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}
2025-07-28 16:49:13.830 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:13.830 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:13.830 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 11
2025-07-28 16:49:13.830 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 189, 320, 251]
2025-07-28 16:49:13.831 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 149, 328, 186]
2025-07-28 16:49:13.831 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 134, 355, 161]
2025-07-28 16:49:13.831 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 11 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.49, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 186], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 161], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.29, 'name': 'car', 'ch_name': 'car', 'xyxy': [509, 65, 531, 84], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.3, 'name': 'car', 'ch_name': 'car', 'xyxy': [1228, 203, 1279, 232], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [1159, 205, 1270, 253], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.35, 'name': 'car', 'ch_name': 'car', 'xyxy': [1170, 182, 1241, 206], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.25, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 193, 1184, 213], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.6, 'name': 'car', 'ch_name': 'car', 'xyxy': [1147, 181, 1225, 211], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.62, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 183, 1206, 210], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [1142, 183, 1190, 212], 'track_id': 11, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:13.829283', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692553.8292923}
2025-07-28 16:49:13.990 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:13.990 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:13.990 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:13.990 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:14.006 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:14.006 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 16:49:14.007 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 189, 320, 251]}
2025-07-28 16:49:14.007 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 189, 320, 251]}, {'label': 2, 'conf': 0.47, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 187]}, {'label': 2, 'conf': 0.35, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 16:49:14.007 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:14.007 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:14.007 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:14.007 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:14.007 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:14.007 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:14.008 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:14.008 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:14.008 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:14.008 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:14.009 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:14.009 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:14.009 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:14.009 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:14.014 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:14.014 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:14.014 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:14.014 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 11 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:14.014 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:14.015 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}
2025-07-28 16:49:14.015 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:14.015 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:14.015 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 11
2025-07-28 16:49:14.016 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[263, 189, 320, 251]
2025-07-28 16:49:14.016 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 149, 328, 187]
2025-07-28 16:49:14.016 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 134, 355, 162]
2025-07-28 16:49:14.016 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 11 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [263, 189, 320, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.47, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 149, 328, 187], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.35, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.29, 'name': 'car', 'ch_name': 'car', 'xyxy': [509, 65, 531, 84], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.3, 'name': 'car', 'ch_name': 'car', 'xyxy': [1228, 203, 1279, 232], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [1159, 205, 1270, 253], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.35, 'name': 'car', 'ch_name': 'car', 'xyxy': [1170, 182, 1241, 206], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.25, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 193, 1184, 213], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.6, 'name': 'car', 'ch_name': 'car', 'xyxy': [1147, 181, 1225, 211], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.62, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 183, 1206, 210], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [1142, 183, 1190, 212], 'track_id': 11, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:14.014296', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692554.0143063}
2025-07-28 16:49:14.189 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:14.189 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:14.189 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:14.189 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:14.207 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:14.207 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 16:49:14.207 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.68, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}
2025-07-28 16:49:14.207 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.68, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}, {'label': 2, 'conf': 0.47, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 148, 328, 187]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 16:49:14.208 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:14.208 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:14.208 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:14.208 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:14.208 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:14.208 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:14.208 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:14.209 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:14.209 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:14.209 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:14.209 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:14.210 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:14.210 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:14.210 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:14.214 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:14.214 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:14.214 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:14.214 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 11 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:14.214 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:14.214 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}
2025-07-28 16:49:14.214 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:14.214 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:14.215 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 11
2025-07-28 16:49:14.215 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 189, 320, 251]
2025-07-28 16:49:14.215 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 148, 328, 187]
2025-07-28 16:49:14.215 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 134, 355, 162]
2025-07-28 16:49:14.216 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 11 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.68, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.47, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 148, 328, 187], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.29, 'name': 'car', 'ch_name': 'car', 'xyxy': [509, 65, 531, 84], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.3, 'name': 'car', 'ch_name': 'car', 'xyxy': [1228, 203, 1279, 232], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [1159, 205, 1270, 253], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.35, 'name': 'car', 'ch_name': 'car', 'xyxy': [1170, 182, 1241, 206], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.25, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 193, 1184, 213], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.6, 'name': 'car', 'ch_name': 'car', 'xyxy': [1147, 181, 1225, 211], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.62, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 183, 1206, 210], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [1142, 183, 1190, 212], 'track_id': 11, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:14.214110', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692554.2141182}
2025-07-28 16:49:14.217 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=True, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:14.217 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:14.217 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 3, 配置区域数量: 0
2025-07-28 16:49:14.217 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:14.217 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:14.217 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_normal:3062 - 使用默认普通颜色: 绿色
2025-07-28 16:49:14.217 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3811 - 绘制完成: 绘制3个检测框, 跳过0个
2025-07-28 16:49:14.219 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1223 - 🔍 正常推送: 任务12, 帧数72, 检测到3个目标，无告警
2025-07-28 16:49:14.443 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:14.443 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:14.443 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:14.443 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:14.461 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:14.461 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 3
2025-07-28 16:49:14.461 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.68, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}
2025-07-28 16:49:14.462 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.68, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 148, 328, 187]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162]}]
2025-07-28 16:49:14.462 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:14.462 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:14.462 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:14.462 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:14.462 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:14.463 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:14.463 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:14.463 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:14.463 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:14.463 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:14.464 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:14.464 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:14.464 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:14.464 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:14.468 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:14.468 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:14.469 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:14.469 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 11 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:14.469 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:14.469 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}
2025-07-28 16:49:14.469 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:14.469 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-07-28 16:49:14.469 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 11
2025-07-28 16:49:14.469 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 189, 320, 251]
2025-07-28 16:49:14.470 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 148, 328, 187]
2025-07-28 16:49:14.470 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 134, 355, 162]
2025-07-28 16:49:14.470 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 11 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.68, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 148, 328, 187], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.29, 'name': 'car', 'ch_name': 'car', 'xyxy': [509, 65, 531, 84], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.3, 'name': 'car', 'ch_name': 'car', 'xyxy': [1228, 203, 1279, 232], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [1159, 205, 1270, 253], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.35, 'name': 'car', 'ch_name': 'car', 'xyxy': [1170, 182, 1241, 206], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.25, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 193, 1184, 213], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.6, 'name': 'car', 'ch_name': 'car', 'xyxy': [1147, 181, 1225, 211], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.62, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 183, 1206, 210], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [1142, 183, 1190, 212], 'track_id': 11, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:14.468775', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'vehicle_count': 11, 'unique_vehicle_count': 11}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692554.4687843}
2025-07-28 16:49:14.647 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_push_monitor_frame:3625 - 推送监控帧: task_id=12, 检测=False, 告警=False, 帧尺寸=1280x674
2025-07-28 16:49:14.647 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3736 - 开始优化绘制: 帧尺寸=1280x674
2025-07-28 16:49:14.648 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3747 - 检测结果数量: 0, 配置区域数量: 0
2025-07-28 16:49:14.648 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes_optimized:3750 - 无检测结果，跳过检测框绘制
2025-07-28 16:49:14.665 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:971 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 16:49:14.665 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:973 - 🔍 任务12 - 检测结果数量: 4
2025-07-28 16:49:14.665 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:975 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.68, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}
2025-07-28 16:49:14.665 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:976 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.68, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [857, 168, 935, 203]}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 148, 328, 187]}]
2025-07-28 16:49:14.666 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:14.666 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1334 - 从config_data获取置信度阈值（最高优先级）: 0.01
2025-07-28 16:49:14.666 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_configure_postprocessor_from_db:1339 - 最终设置后处理器置信度阈值: 0.01
2025-07-28 16:49:14.667 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1040 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 16:49:14.667 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1041 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 16:49:14.667 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1043 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 16:49:14.667 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 16:49:14.667 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1061 - 从统一配置获取置信度阈值: 0.01
2025-07-28 16:49:14.668 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 16:49:14.668 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1072 - 从统一配置获取NMS阈值: 0.5
2025-07-28 16:49:14.668 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 16:49:14.668 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1083 - 从统一配置获取输入尺寸: 640
2025-07-28 16:49:14.669 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1116 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 16:49:14.669 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1120 - 传递给后处理器的检测结果数量: N/A
2025-07-28 16:49:14.673 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1125 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 16:49:14.673 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1127 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 16:49:14.673 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1129 - ⚠️ 任务12 - hit: False
2025-07-28 16:49:14.673 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1130 - ⚠️ 任务12 - message: 检测到 12 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 16:49:14.674 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1133 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'vehicle_tracking']
2025-07-28 16:49:14.674 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1138 - 🚗 任务12 - 车辆跟踪信息: {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], 'vehicle_count': 12, 'unique_vehicle_count': 12}
2025-07-28 16:49:14.674 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1139 - 🚗 任务12 - 车辆ID列表: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
2025-07-28 16:49:14.674 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1140 - 🚗 任务12 - 唯一车辆ID: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
2025-07-28 16:49:14.674 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1145 - ⚠️ 任务12 - 检测结果数量: 12
2025-07-28 16:49:14.674 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测1: track_id=1, bbox=[262, 189, 320, 251]
2025-07-28 16:49:14.675 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测2: track_id=2, bbox=[290, 148, 328, 187]
2025-07-28 16:49:14.675 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1149 - ⚠️ 任务12 - 检测3: track_id=3, bbox=[320, 134, 355, 162]
2025-07-28 16:49:14.675 | be40b71884184b3db68a19d5057154ca | DEBUG    | module_stream.service.task_execution_service:detection_loop:1177 - 后处理结果: {'hit': False, 'message': '检测到 12 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.68, 'name': 'car', 'ch_name': 'car', 'xyxy': [262, 189, 320, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [290, 148, 328, 187], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [320, 134, 355, 162], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.29, 'name': 'car', 'ch_name': 'car', 'xyxy': [509, 65, 531, 84], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.3, 'name': 'car', 'ch_name': 'car', 'xyxy': [1228, 203, 1279, 232], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [1159, 205, 1270, 253], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.35, 'name': 'car', 'ch_name': 'car', 'xyxy': [1170, 182, 1241, 206], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.25, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 193, 1184, 213], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.6, 'name': 'car', 'ch_name': 'car', 'xyxy': [1147, 181, 1225, 211], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.62, 'name': 'car', 'ch_name': 'car', 'xyxy': [1143, 183, 1206, 210], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [1142, 183, 1190, 212], 'track_id': 11, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [857, 168, 935, 203], 'track_id': 12, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T16:49:14.673618', 'vehicle_tracking': {'vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], 'unique_vehicle_ids': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], 'vehicle_count': 12, 'unique_vehicle_count': 12}, 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753692554.6736286}
2025-07-28 16:49:14.694 | 81cc8a56d9804be3809f310c0c9f3ac3 | INFO     | module_stream.service.task_execution_service:stop_task:306 - 开始停止任务: 12
2025-07-28 16:49:14.696 | 81cc8a56d9804be3809f310c0c9f3ac3 | INFO     | module_stream.service.task_execution_service:stop_task:322 - 取消异步任务: 12
2025-07-28 16:49:14.696 | be40b71884184b3db68a19d5057154ca | INFO     | module_stream.service.task_execution_service:detection_loop:1267 - 检测任务被取消: 12
2025-07-28 16:49:14.719 | 81cc8a56d9804be3809f310c0c9f3ac3 | INFO     | module_stream.service.task_execution_service:stop_task:330 - 已从运行任务列表移除: 12
2025-07-28 16:49:14.719 | 81cc8a56d9804be3809f310c0c9f3ac3 | INFO     | module_stream.service.task_execution_service:stop_task:339 - 任务 12 使用智驱力直接集成，无需清理外部进程
2025-07-28 16:49:14.719 | 81cc8a56d9804be3809f310c0c9f3ac3 | INFO     | module_stream.service.task_execution_service:stop_monitor_stream:3882 - 任务 12 的监控流已停止
2025-07-28 16:49:14.719 | 81cc8a56d9804be3809f310c0c9f3ac3 | INFO     | module_stream.service.task_execution_service:stop_task:344 - 监控流停止成功: 12
2025-07-28 16:49:14.719 | 81cc8a56d9804be3809f310c0c9f3ac3 | INFO     | module_stream.service.task_execution_service:_clear_task_cache:133 - 任务12缓存已清除
2025-07-28 16:49:14.719 | 81cc8a56d9804be3809f310c0c9f3ac3 | INFO     | module_stream.service.task_execution_service:stop_task:353 - 任务缓存清理成功: 12
2025-07-28 16:49:14.728 | 81cc8a56d9804be3809f310c0c9f3ac3 | INFO     | module_stream.service.task_execution_service:stop_task:362 - 任务状态更新成功: 12
2025-07-28 16:49:14.729 | 81cc8a56d9804be3809f310c0c9f3ac3 | INFO     | module_stream.service.task_execution_service:stop_task:374 - 任务 12 停止成功，包括实时监控流
2025-07-28 16:49:14.729 | 81cc8a56d9804be3809f310c0c9f3ac3 | INFO     | module_stream.controller.monitor_controller:batch_stop_tasks:209 - 批量停止任务成功: [12]
2025-07-28 16:49:14.768 |  | INFO     | module_stream.controller.monitor_websocket_controller:_start_stream_push:180 - 客户端 842372e5-d7fc-40f9-9e09-5e1a932bf4bb 主动断开连接
2025-07-28 16:49:14.768 |  | INFO     | module_stream.controller.monitor_websocket_controller:_cleanup_connection:209 - 客户端 842372e5-d7fc-40f9-9e09-5e1a932bf4bb 连接已清理
2025-07-28 16:49:24.765 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 16:49:24.766 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
